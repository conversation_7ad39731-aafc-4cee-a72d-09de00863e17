<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GemAI - Pricing Plans</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #E0F2FE 0%, #FCE7F3 100%);
        }
        .dark .gradient-bg {
            background: linear-gradient(135deg, #1e293b 0%, #3b0764 100%);
        }
    </style>
    <script>
        if (localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark')
        } else {
            document.documentElement.classList.remove('dark')
        }
    </script>
</head>
<body class="dark:bg-gray-900">
    <!-- Navigation -->
    <nav class="bg-white dark:bg-gray-800 shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <div class="flex items-center">
                    <a href="index.html" class="text-2xl font-bold text-blue-600 dark:text-blue-400">GemAI</a>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">Product</a>
                    <a href="pricing.html" class="text-blue-600 dark:text-blue-400">Pricing</a>
                    <a href="#" class="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">Resources</a>
                    <a href="#" class="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">Blog</a>
                </div>
                <div class="hidden md:flex items-center space-x-4">
                    <!-- Dark Mode Toggle -->
                    <button id="darkModeToggle" class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                        <!-- Sun Icon (shown in dark mode) -->
                        <svg class="w-6 h-6 hidden dark:block text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
                        </svg>
                        <!-- Moon Icon (shown in light mode) -->
                        <svg class="w-6 h-6 block dark:hidden text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
                        </svg>
                    </button>
                    <a href="#" class="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">Sign in</a>
                    <a href="#" class="bg-blue-600 text-white dark:bg-blue-500 px-4 py-2 rounded-full hover:bg-blue-700 dark:hover:bg-blue-600">Sign up</a>
                </div>
                <div class="md:hidden" id="menubtn">
                    <button class="text-gray-600 dark:text-gray-300">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Pricing Header -->
    <section class="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-white to-gray-50 dark:from-gray-900 dark:to-gray-800">
        <div class="max-w-7xl mx-auto text-center">
            <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">Simple, transparent pricing</h1>
            <p class="text-xl text-gray-600 dark:text-gray-300 mb-12 max-w-2xl mx-auto">Choose the perfect plan for your team. All plans include a 14-day free trial.</p>
            
            <!-- Billing Toggle -->
            <div class="flex items-center justify-center space-x-4 mb-12">
                <span class="text-gray-600 dark:text-gray-300">Monthly</span>
                <button class="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 dark:bg-gray-700">
                    <span class="inline-block h-4 w-4 transform rounded-full bg-white transition translate-x-6"></span>
                </button>
                <span class="text-gray-600 dark:text-gray-300">Annual <span class="text-green-600 dark:text-green-400">Save 20%</span></span>
            </div>
        </div>
    </section>

    <!-- Pricing Cards -->
    <section class="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-800">
        <div class="max-w-7xl mx-auto">
            <div class="grid md:grid-cols-3 gap-8">
                <!-- Starter Plan -->
                <div class="bg-white dark:bg-gray-900 rounded-2xl shadow-sm p-8 hover:shadow-lg transition-shadow">
                    <div class="mb-8">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Starter</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">Perfect for small teams getting started</p>
                        <div class="flex items-baseline">
                            <span class="text-4xl font-bold text-gray-900 dark:text-white">$49</span>
                            <span class="text-gray-600 dark:text-gray-300 ml-2">/month</span>
                        </div>
                    </div>
                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center text-gray-600 dark:text-gray-300">
                            <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            Up to 5 team members
                        </li>
                        <li class="flex items-center text-gray-600 dark:text-gray-300">
                            <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            Basic AI features
                        </li>
                        <li class="flex items-center text-gray-600 dark:text-gray-300">
                            <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            10GB storage
                        </li>
                        <li class="flex items-center text-gray-600 dark:text-gray-300">
                            <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            Email support
                        </li>
                    </ul>
                    <button class="w-full bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white px-6 py-3 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
                        Start free trial
                    </button>
                </div>

                <!-- Pro Plan -->
                <div class="bg-white dark:bg-gray-900 rounded-2xl shadow-sm p-8 hover:shadow-lg transition-shadow relative">
                    <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                        <span class="bg-blue-600 text-white px-4 py-1 rounded-full text-sm">Most Popular</span>
                    </div>
                    <div class="mb-8">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Pro</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">Best for growing businesses</p>
                        <div class="flex items-baseline">
                            <span class="text-4xl font-bold text-gray-900 dark:text-white">$99</span>
                            <span class="text-gray-600 dark:text-gray-300 ml-2">/month</span>
                        </div>
                    </div>
                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center text-gray-600 dark:text-gray-300">
                            <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            Up to 20 team members
                        </li>
                        <li class="flex items-center text-gray-600 dark:text-gray-300">
                            <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            Advanced AI features
                        </li>
                        <li class="flex items-center text-gray-600 dark:text-gray-300">
                            <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            100GB storage
                        </li>
                        <li class="flex items-center text-gray-600 dark:text-gray-300">
                            <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            Priority support
                        </li>
                        <li class="flex items-center text-gray-600 dark:text-gray-300">
                            <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            Custom integrations
                        </li>
                    </ul>
                    <button class="w-full bg-blue-600 dark:bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors">
                        Start free trial
                    </button>
                </div>

                <!-- Enterprise Plan -->
                <div class="bg-white dark:bg-gray-900 rounded-2xl shadow-sm p-8 hover:shadow-lg transition-shadow">
                    <div class="mb-8">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Enterprise</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">For large organizations</p>
                        <div class="flex items-baseline">
                            <span class="text-4xl font-bold text-gray-900 dark:text-white">Custom</span>
                        </div>
                    </div>
                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center text-gray-600 dark:text-gray-300">
                            <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            Unlimited team members
                        </li>
                        <li class="flex items-center text-gray-600 dark:text-gray-300">
                            <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            Custom AI solutions
                        </li>
                        <li class="flex items-center text-gray-600 dark:text-gray-300">
                            <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            Unlimited storage
                        </li>
                        <li class="flex items-center text-gray-600 dark:text-gray-300">
                            <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            24/7 dedicated support
                        </li>
                        <li class="flex items-center text-gray-600 dark:text-gray-300">
                            <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            Custom development
                        </li>
                    </ul>
                    <button class="w-full bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white px-6 py-3 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
                        Contact sales
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Feature Comparison -->
    <section class="py-20 px-4 sm:px-6 lg:px-8 bg-white dark:bg-gray-900">
        <div class="max-w-7xl mx-auto">
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white text-center mb-12">Compare Features</h2>
            
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="border-b dark:border-gray-700">
                            <th class="text-left py-4 px-6 text-gray-600 dark:text-gray-300">Features</th>
                            <th class="text-center py-4 px-6 text-gray-600 dark:text-gray-300">Starter</th>
                            <th class="text-center py-4 px-6 text-gray-600 dark:text-gray-300">Pro</th>
                            <th class="text-center py-4 px-6 text-gray-600 dark:text-gray-300">Enterprise</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="border-b dark:border-gray-700">
                            <td class="py-4 px-6 text-gray-900 dark:text-white">AI Sales Forecasting</td>
                            <td class="py-4 px-6 text-center text-gray-600 dark:text-gray-300">Basic</td>
                            <td class="py-4 px-6 text-center text-gray-600 dark:text-gray-300">Advanced</td>
                            <td class="py-4 px-6 text-center text-gray-600 dark:text-gray-300">Custom</td>
                        </tr>
                        <tr class="border-b dark:border-gray-700">
                            <td class="py-4 px-6 text-gray-900 dark:text-white">CRM Integration</td>
                            <td class="py-4 px-6 text-center text-gray-600 dark:text-gray-300">Limited</td>
                            <td class="py-4 px-6 text-center text-gray-600 dark:text-gray-300">Full</td>
                            <td class="py-4 px-6 text-center text-gray-600 dark:text-gray-300">Custom</td>
                        </tr>
                        <tr class="border-b dark:border-gray-700">
                            <td class="py-4 px-6 text-gray-900 dark:text-white">Analytics Dashboard</td>
                            <td class="py-4 px-6 text-center text-gray-600 dark:text-gray-300">Basic</td>
                            <td class="py-4 px-6 text-center text-gray-600 dark:text-gray-300">Advanced</td>
                            <td class="py-4 px-6 text-center text-gray-600 dark:text-gray-300">Custom</td>
                        </tr>
                        <tr class="border-b dark:border-gray-700">
                            <td class="py-4 px-6 text-gray-900 dark:text-white">API Access</td>
                            <td class="py-4 px-6 text-center text-gray-600 dark:text-gray-300">No</td>
                            <td class="py-4 px-6 text-center text-gray-600 dark:text-gray-300">Yes</td>
                            <td class="py-4 px-6 text-center text-gray-600 dark:text-gray-300">Custom</td>
                        </tr>
                        <tr class="border-b dark:border-gray-700">
                            <td class="py-4 px-6 text-gray-900 dark:text-white">Custom Reports</td>
                            <td class="py-4 px-6 text-center text-gray-600 dark:text-gray-300">No</td>
                            <td class="py-4 px-6 text-center text-gray-600 dark:text-gray-300">Yes</td>
                            <td class="py-4 px-6 text-center text-gray-600 dark:text-gray-300">Custom</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-800">
        <div class="max-w-7xl mx-auto">
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white text-center mb-12">Frequently Asked Questions</h2>
            
            <div class="grid md:grid-cols-2 gap-8">
                <div class="space-y-4">
                    <div class="bg-white dark:bg-gray-900 rounded-xl shadow-sm">
                        <button class="w-full text-left px-6 py-4 flex items-center justify-between">
                            <span class="font-semibold text-gray-900 dark:text-white">Can I change plans later?</span>
                            <svg class="w-6 h-6 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </button>
                    </div>
                    <div class="bg-white dark:bg-gray-900 rounded-xl shadow-sm">
                        <button class="w-full text-left px-6 py-4 flex items-center justify-between">
                            <span class="font-semibold text-gray-900 dark:text-white">What payment methods do you accept?</span>
                            <svg class="w-6 h-6 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="space-y-4">
                    <div class="bg-white dark:bg-gray-900 rounded-xl shadow-sm">
                        <button class="w-full text-left px-6 py-4 flex items-center justify-between">
                            <span class="font-semibold text-gray-900 dark:text-white">Is there a free trial?</span>
                            <svg class="w-6 h-6 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </button>
                    </div>
                    <div class="bg-white dark:bg-gray-900 rounded-xl shadow-sm">
                        <button class="w-full text-left px-6 py-4 flex items-center justify-between">
                            <span class="font-semibold text-gray-900 dark:text-white">Do you offer refunds?</span>
                            <svg class="w-6 h-6 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-white to-blue-50 dark:from-gray-900 dark:to-gray-800">
        <div class="max-w-7xl mx-auto text-center">
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">Ready to get started?</h2>
            <p class="text-xl text-gray-600 dark:text-gray-300 mb-12">Join thousands of teams using GemAI to boost their sales</p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="#" class="bg-blue-600 dark:bg-blue-500 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-blue-700 dark:hover:bg-blue-600">
                    Start free trial
                </a>
                <a href="#" class="bg-white dark:bg-gray-800 text-blue-600 dark:text-blue-400 px-8 py-4 rounded-lg text-lg font-semibold border border-blue-600 dark:border-blue-500 hover:bg-blue-50 dark:hover:bg-gray-700">
                    Contact sales
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-white dark:bg-gray-900 text-gray-300">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-12">
                <!-- Company Info -->
                <div class="lg:col-span-2">
                    <a href="#" class="text-2xl font-bold text-black dark:text-white mb-6 block">GemAI</a>
                    <p class="text-gray-400 dark:text-gray-500 mb-8 max-w-md">Empowering sales teams with AI-driven insights and automation to close more deals and drive unprecedented growth.</p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path fill-rule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clip-rule="evenodd"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path fill-rule="evenodd" d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" clip-rule="evenodd"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Product Links -->
                <div>
                    <h3 class="text-black dark:text-white font-semibold mb-4">Product</h3>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Features</a></li>
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Pricing</a></li>
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Security</a></li>
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Enterprise</a></li>
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Customer Stories</a></li>
                    </ul>
                </div>

                <!-- Resources Links -->
                <div>
                    <h3 class="text-black dark:text-white font-semibold mb-4">Resources</h3>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Documentation</a></li>
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">API Reference</a></li>
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Blog</a></li>
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Community</a></li>
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Support</a></li>
                    </ul>
                </div>

                <!-- Company Links -->
                <div>
                    <h3 class="text-black dark:text-white font-semibold mb-4">Company</h3>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">About Us</a></li>
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Careers</a></li>
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Partners</a></li>
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Contact</a></li>
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Press Kit</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Bottom Footer -->
        <div class="border-t border-gray-100 dark:border-gray-800">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="md:flex md:items-center md:justify-between">
                    <div class="text-sm text-gray-400 dark:text-gray-500">
                        © 2024 GemAI. All rights reserved.
                    </div>
                    <div class="flex space-x-6 mt-4 md:mt-0">
                        <a href="#" class="text-sm text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Privacy Policy</a>
                        <a href="#" class="text-sm text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Terms of Service</a>
                        <a href="#" class="text-sm text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Cookie Policy</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        const menuButton = document.getElementById('menubtn');
        const mobileMenu = document.getElementById('mobilebtn');
        
        menuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });

        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        
        // Check for saved theme preference or use system preference
        if (localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
        
        darkModeToggle.addEventListener('click', () => {
            if (document.documentElement.classList.contains('dark')) {
                document.documentElement.classList.remove('dark');
                localStorage.theme = 'light';
            } else {
                document.documentElement.classList.add('dark');
                localStorage.theme = 'dark';
            }
        });
    </script>
</body>
</html> 