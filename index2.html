<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>GemAI - URL Shortener & Bio Pages</title>
        <link rel="stylesheet" href="style.css">    
    </head>
    <body>
        <div class="relative overflow-hidden">
            <div class="absolute inset-0 z-0 opacity-30">
                <div class="absolute inset-0 bg-[radial-gradient(#818cf8_1px,transparent_1px)] [background-size:20px_20px]"></div>
            </div>
            <header class="bg-white shadow-sm absolute z-50 top-0 inset-x-0">
            <nav class="items-center justify-between mx-auto flex max-w-screen-2xl p-4">
                <div class="lg:flex-1 flex">
                <a href="#" class="-m-1.5 p-1.5">
                    <span class="sr-only">Windframe</span>
                    <img alt="" src="https://res.cloudinary.com/speedwares/image/upload/v1659284687/windframe-logo-main_daes7r.png" class="h-8 w-auto">
                </a>
                </div>
                <a class="px-5 py-2.5 rounded-md bg-black text-white">Get Started</a>
            </nav>
            </header>
            <section class="mx-auto mt-16 pt-24 pb-28 relative max-w-screen-2xl p-4 isolate">
            <svg class="w-[468px] h-[788px] absolute top-0 left-0 -z-10 transform-gpu overflow-hidden blur-2xl" viewBox="0 0
                468 788" fill="none" xmlns="http://www.w3.org/2000/svg" id="Windframe_SgwBQo1NI9"><circle cx="44.5105" cy="378.637" r="156.383" fill="#4A3AFF"></circle><circle cx="119.803" cy="529.24" r="156.383" fill="#702DFF"></circle><circle cx="173.364" cy="372.857" r="156.383" fill="#2D5BFF"></circle><g filter="url(#filter0_b_1410_520)"><circle cx="73.5409" cy="394.049" r="393.819" fill="white" fill-opacity="0.79"></circle></g><defs><filter x="-460.404" y="-139.896" width="1067.89" height="1067.89" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood><feGaussianBlur in="BackgroundImageFix" stdDeviation="70.063"></feGaussianBlur><feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1410_520"></feComposite><feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1410_520" result="shape"></feBlend></filter></defs></svg>
            <div class="mx-auto max-w-2xl">
                <p class="text-center text-gray-500 font-medium tracking-wide uppercase uppercase">tailwind page builder</p>
                <p class="mt-2 text-center text-4xl md:text-6xl font-bold tracking-tight text-black">The best way to organize
                    your work</p>
                <p class="mt-6 text-lg text-gray-600 text-center">Want to receive a monthly email in your inbox with awesome
                    free tips and resources on productivity? Please submit your email below.</p>
                <div class="mt-24 w-full bg-white rounded-lg drop-shadow-xl p-2.5 flex border border-gray-300">
                <input type="email" class="border-none focus:outline-none p-2.5 flex-1 flex-shrink-0 text-lg w-full">
                <button type="submit" style="font-family: Arial" class="px-5 py-2.5 rounded-md bg-black
                    text-white">Subscribe</button>
                </div>
                <div class="items-center justify-center mt-6 flex gap-3">
                <svg class="w-6 h-6" viewBox="0 0 23 23" fill="none" xmlns="http://www.w3.org/2000/svg" id="Windframe_pfh-M4e9OwK"><path d="M11.0527 22.0752C17.128 22.0752 22.0527 17.1505 22.0527 11.0752C22.0527
                    4.9999 17.128 0.0751953 11.0527 0.0751953C4.97743 0.0751953 0.0527344 4.9999 0.0527344 11.0752C0.0527344
                    17.1505 4.97743 22.0752 11.0527 22.0752Z" fill="currentColor"></path><path d="M5.55273 11.6251L8.85273
                    14.9251L16.5527 7.2251" stroke="white" strokewidth="1.5" strokelinecap="round" strokelinejoin="round"></path></svg>
                <p class="text-center text-gray-600">It's 100% free and we will never send more than one email per month</p>
                </div>
            </div>
            </section>
        </div>
        <section class="mx-auto py-20 max-w-screen-2xl p-4">
            <p class="text-center text-gray-600 font-medium tracking-wide uppercase">trusted by</p>
            <div class="md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 mt-10 grid place-items-center gap-10">
            <div class="h-10 w-auto">
                <svg class="w-full h-full" viewBox="0 0 306 78" fill="none" xmlns="http://www.w3.org/2000/svg" id="Windframe_qmTMvWhiQWG"><path fillrule="evenodd" clip-rule="evenodd" d="M97.8958 60.9676L101.715
                    52.0936C105.84 55.1781 111.335 56.7815 116.758 56.7815C120.76 56.7815 123.294 55.2393 123.294
                    52.9015C123.233 46.3653 99.3157 51.4816 99.1321 35.0923C99.0709 26.7691 106.464 20.3553 116.941
                    20.3553C123.171 20.3553 129.389 21.8976 133.832 25.4105L130.258 34.4681C126.195 31.8732 121.14 30.0249
                    116.329 30.0249C113.061 30.0249 110.907 31.5672 110.907 33.5378C110.968 39.9516 135.069 36.4387 135.313
                    52.0936C135.313 60.6004 128.104 66.5858 117.749 66.5858C110.16 66.5735 103.196 64.7865 97.8958
                    60.9676ZM244.543 48.8868C242.634 52.216 239.06 54.4927 234.922 54.4927C228.815 54.4927 223.894 49.56 223.894
                    43.4644C223.894 37.3689 228.827 32.4362 234.922 32.4362C239.047 32.4362 242.634 34.7128 244.543
                    38.0421L255.082 32.1914C251.14 25.1657 243.552 20.3553 234.922 20.3553C222.156 20.3553 211.801 30.7104
                    211.801 43.4767C211.801 56.243 222.156 66.598 234.922 66.598C243.613 66.598 251.14 61.8489 255.082
                    54.7619L244.543 48.8868ZM140.613 1.17529H153.808V65.7167H140.613V1.17529ZM260.259
                    1.17529V65.7167H273.454V46.3531L289.109 65.7167H306L286.086 42.7178L304.519 21.2611H288.362L273.442
                    39.0703V1.17529H260.259Z" fill="currentColor"></path><path d="M193.012 49.0093C191.102 52.155 187.161 54.4928
                    182.718 54.4928C176.61 54.4928 171.69 49.5601 171.69 43.4646C171.69 37.3691 176.622 32.4364 182.718
                    32.4364C187.161 32.4364 191.102 34.8966 193.012 38.1035V49.0093ZM193.012 21.2735V26.5122C190.858 22.8769
                    185.496 20.3433 179.878 20.3433C168.287 20.3433 159.168 30.5759 159.168 43.4034C159.168 56.2309 168.287
                    66.5859 179.878 66.5859C185.484 66.5859 190.845 64.0645 193.012 60.417V65.6557H206.206V21.2735H193.012Z" fill="currentColor"></path><path fillrule="evenodd" clip-rule="evenodd" d="M16.3399 48.9482C16.3399 53.4525
                    12.7046 57.0878 8.20027 57.0878C3.69596 57.0878 0.0606842 53.4403 0.0606842 48.9482C0.0606842 44.4561
                    3.69596 40.8086 8.20027 40.8086H16.3399V48.9482ZM20.4035 48.9482C20.4035 44.4439 24.0388 40.8086 28.5431
                    40.8086C33.0474 40.8086 36.6827 44.4439 36.6827 48.9482V69.291C36.6827 73.7953 33.0474 77.4306 28.5431
                    77.4306C24.0388 77.4306 20.4035 73.7953 20.4035 69.291V48.9482Z" fill="currentColor"></path><path fillrule="evenodd" clip-rule="evenodd" d="M28.5436 16.2792C24.0393 16.2792 20.4041 12.6439 20.4041
                    8.13959C20.4041 3.63528 24.0393 0 28.5436 0C33.048 0 36.6832 3.63528 36.6832
                    8.13959V16.2792H28.5436ZM28.5436 20.4041C33.048 20.4041 36.6832 24.0393 36.6832 28.5436C36.6832 33.048
                    33.048 36.6832 28.5436 36.6832H8.13959C3.63528 36.6832 0 33.0357 0 28.5436C0 24.0516 3.63528 20.4041 8.13959
                    20.4041H28.5436Z" fill="currentColor"></path><path fillrule="evenodd" clip-rule="evenodd" d="M61.1507
                    28.5436C61.1507 24.0393 64.7859 20.4041 69.2902 20.4041C73.7946 20.4041 77.4298 24.0393 77.4298
                    28.5436C77.4298 33.048 73.7946 36.6832 69.2902 36.6832H61.1507V28.5436ZM57.087 28.5436C57.087 33.048 53.4517
                    36.6832 48.9474 36.6832C44.4431 36.6832 40.8078 33.048 40.8078 28.5436V8.13959C40.8078 3.63528 44.4431 0
                    48.9474 0C53.4517 0 57.087 3.63528 57.087 8.13959V28.5436Z" fill="currentColor"></path><path fillrule="evenodd" clip-rule="evenodd" d="M48.9474 61.1514C53.4517 61.1514 57.087 64.7867 57.087 69.291C57.087 73.7953 53.4517
                    77.4306 48.9474 77.4306C44.4431 77.4306 40.8078 73.7953 40.8078 69.291V61.1514H48.9474ZM48.9474
                    57.0878C44.4431 57.0878 40.8078 53.4525 40.8078 48.9482C40.8078 44.4439 44.4431 40.8086 48.9474
                    40.8086H69.3514C73.8558 40.8086 77.491 44.4439 77.491 48.9482C77.491 53.4525 73.8558 57.0878 69.3514
                    57.0878H48.9474Z" fill="currentColor"></path></svg>
            </div>
            <div class="h-10 w-auto">
                <svg class="w-ful h-full" viewBox="0 0 292 96" fill="none" xmlns="http://www.w3.org/2000/svg" id="Windframe_vLwg796ydA7"><path d="M35.9063 0H38.4989C47.4996 0.195667 56.3535 3.81551 62.7127
                    10.2236C60.3647 12.6205 57.9678 14.9196 55.6687 17.3165C52.0978 14.088 47.7931 11.5933 43.0971
                    10.7128C36.1509 9.24527 28.6666 10.566 22.8456 14.5283C16.4864 18.6862 12.1817 25.7302 11.2523
                    33.2634C10.225 40.6988 12.3284 48.5254 17.1712 54.2976C21.8183 59.923 28.9112 63.4451 36.2487
                    63.6896C43.0971 64.081 50.2389 61.9776 55.2285 57.1837C59.1418 53.8084 60.9517 48.6722 61.5387
                    43.6827C53.4185 43.6827 45.2983 43.7316 37.1782 43.6827V33.6058H71.371C73.132 44.4164 70.5883 56.5967
                    62.3703 64.3256C56.8916 69.8042 49.3095 73.0327 41.5807 73.6687C34.0964 74.4024 26.3676 72.9838 19.7149
                    69.364C11.7414 65.1082 5.38227 57.9175 2.25159 49.4549C-0.683411 41.6771 -0.732328 32.8721 2.00701
                    25.0454C4.50177 17.9035 9.29561 11.5933 15.4591 7.19077C21.3781 2.83717 28.5688 0.440251 35.9063 0Z" fill="currentColor"></path><path d="M232.063 2.54346H242.532V72.3966C239.059 72.3966 235.537 72.4455 232.063
                    72.3477C232.112 49.1122 232.063 25.8278 232.063 2.54346Z" fill="currentColor"></path><path d="M94.6552
                    26.7577C101.112 25.5347 108.107 26.9044 113.439 30.7688C118.282 34.193 121.657 39.5739 122.684
                    45.4439C124.005 52.2433 122.342 59.6786 117.94 65.0595C113.195 71.0763 105.417 74.3048 97.8348
                    73.8156C90.8886 73.4243 84.1381 69.9512 80.0291 64.2768C75.382 58.0155 74.2569 49.4061 76.8006
                    42.0686C79.3442 34.193 86.535 28.1762 94.6552 26.7577ZM96.1227 36.0518C93.4812 36.7367 91.0353 38.2531
                    89.2743 40.4054C84.5294 46.0798 84.8229 55.3251 90.057 60.6081C93.0409 63.6409 97.5413 65.0595 101.699
                    64.2279C105.564 63.5431 108.939 60.9505 110.798 57.5263C114.026 51.7052 113.097 43.6829 108.156
                    39.1336C104.977 36.1986 100.33 34.9757 96.1227 36.0518Z" fill="currentColor"></path><path d="M146.507
                    26.7574C153.894 25.3388 161.965 27.3933 167.493 32.5784C176.493 40.6497 177.472 55.765 169.792
                    65.0592C165.145 70.9292 157.611 74.1577 150.176 73.8153C143.083 73.6196 136.088 70.0976 131.881
                    64.2765C127.136 57.8684 126.109 49.0634 128.799 41.5791C131.49 33.9481 138.534 28.127 146.507
                    26.7574ZM147.975 36.0515C145.333 36.7364 142.888 38.2528 141.127 40.3562C136.431 45.9327 136.626 55.0312
                    141.665 60.3632C144.649 63.5428 149.296 65.1081 153.6 64.2276C157.416 63.4939 160.84 60.9502 162.699
                    57.526C165.878 51.656 164.949 43.6336 159.959 39.0844C156.78 36.1494 152.133 34.9754 147.975 36.0515Z" fill="currentColor"></path><path d="M190.484 29.5946C196.11 26.0726 203.594 25.0942 209.66 28.1271C211.567 28.9587
                    213.133 30.3773 214.649 31.7958C214.698 30.4751 214.649 29.1054 214.698 27.7357C217.976 27.7847 221.253
                    27.7357 224.579 27.7847V70.9293C224.53 77.4352 222.867 84.3325 218.171 89.0774C213.035 94.3115 205.159
                    95.9257 198.066 94.8495C190.484 93.7245 183.881 88.1969 180.946 81.2018C183.881 79.7832 186.962 78.6581
                    189.995 77.3374C191.707 81.3485 195.18 84.7727 199.534 85.5554C203.887 86.338 208.926 85.2619 211.763
                    81.642C214.796 77.9244 214.796 72.837 214.649 68.2878C212.399 70.489 209.806 72.4457 206.676 73.1794C199.876
                    75.0872 192.392 72.7392 187.158 68.141C181.875 63.5428 178.744 56.4499 179.038 49.4059C179.185 41.4324
                    183.685 33.7525 190.484 29.5946ZM200.61 35.8559C197.626 36.3451 194.838 38.0083 192.93 40.3074C188.332
                    45.786 188.332 54.5421 192.979 59.923C195.621 63.1026 199.876 64.8636 203.985 64.4233C207.85 64.032 211.421
                    61.5862 213.328 58.2109C216.557 52.4876 216.019 44.7099 211.665 39.7204C208.975 36.6386 204.67 35.1222
                    200.61 35.8559Z" fill="currentColor"></path><path d="M255.103 32.0896C260.973 26.6109 270.023 24.7521 277.556
                    27.6382C284.698 30.3286 289.247 37.128 291.546 44.1231C280.931 48.5256 270.365 52.8792 259.75
                    57.2817C261.218 60.07 263.468 62.6137 266.501 63.6409C270.756 65.1573 275.844 64.6193 279.415
                    61.7821C280.833 60.7059 281.958 59.2873 283.035 57.9177C285.725 59.7276 288.415 61.4886 291.106
                    63.2985C287.29 69.0218 280.882 73.0329 273.985 73.6689C266.354 74.5983 258.234 71.6633 253.293
                    65.6954C245.173 56.3034 245.956 40.5522 255.103 32.0896ZM260.337 41.1392C258.674 43.5361 257.989 46.4711
                    258.038 49.3572C265.131 46.4222 272.224 43.4872 279.317 40.5033C278.143 37.7639 275.306 36.1008 272.42
                    35.6605C267.773 34.8289 262.93 37.3237 260.337 41.1392Z" fill="currentColor"></path></svg>
            </div>
            <div class="h-10 w-auto">
                <svg class="w-full h-full" viewBox="0 0 295 72" fill="none" xmlns="http://www.w3.org/2000/svg" id="Windframe_zen5DEFVpES"><path fillrule="evenodd" clip-rule="evenodd" d="M0 52.8024L10.5715 44.7051C16.1853
                    52.0343 22.1469 55.4118 28.7901 55.4118C35.3964 55.4118 41.1942 52.0733 46.5564 44.802L57.2735
                    52.7055C49.5422 63.1892 39.9234 68.728 28.7901 68.728C17.6936 68.728 7.98562 63.2281 0 52.8024Z" fill="currentColor"></path><path fillrule="evenodd" clip-rule="evenodd" d="M28.7521 17.6101L9.93652
                    33.8232L1.24411 23.7355L28.7895 0L56.1208 23.754L47.3855 33.8047L28.7521 17.6101Z" fill="currentColor"></path><path d="M129.433 13.687H118.78V58.0744H129.433V13.687Z" fill="currentColor"></path><path fillrule="evenodd" clip-rule="evenodd" d="M97.2522 58.2708C90.9723 58.2708 85.739 56.2357 81.4942 52.1653C77.2495 48.0368
                    75.1561 42.7454 75.1561 36.291C75.1561 29.7785 77.3076 24.4289 81.5524 20.2424C85.8552 15.9976 91.0885
                    13.9043 97.3104 13.9043C104.986 13.9043 111.789 17.2187 115.743 22.3938L108.823 29.6623C105.509 26.1153
                    101.904 24.3127 98.008 24.3127C94.6937 24.3127 91.9608 25.4175 89.693 27.6852C87.4834 29.953 86.3786 32.8023
                    86.3786 36.2329C86.3786 39.5473 87.4834 42.3384 89.693 44.6061C91.9608 46.8158 94.6937 47.9205 97.95
                    47.9205C102.195 47.9205 105.916 46.0598 108.998 42.3965L116.209 49.3161C114.232 51.9327 111.556 54.0841
                    108.242 55.7704C104.928 57.4567 101.264 58.2708 97.2522 58.2708Z" fill="currentColor"></path><path fillrule="evenodd" clip-rule="evenodd" d="M145.413 17.2386C145.413 20.4255 142.831 23.009 139.644
                    23.009C136.456 23.009 133.873 20.4255 133.873 17.2386C133.873 14.0517 136.456 11.4683 139.644
                    11.4683C142.831 11.4683 145.413 14.0517 145.413 17.2386ZM144.969 25.6721H134.317V58.0749H144.969V25.6721Z" fill="currentColor"></path><path fillrule="evenodd" clip-rule="evenodd" d="M165.559 58.3284C160.5 58.3284 156.43
                    56.7584 153.232 53.6184C150.092 50.4785 148.521 46.4663 148.521 41.5238C148.521 36.5813 150.092 32.569
                    153.29 29.4292C156.488 26.2892 160.616 24.7192 165.733 24.7192C172.421 24.7192 178.293 27.9754 181.026
                    34.081L172.827 38.3838C171.199 35.6509 168.932 34.2554 165.966 34.2554C163.815 34.2554 162.07 34.9532
                    160.675 36.3486C159.337 37.7442 158.64 39.4886 158.64 41.5238C158.64 45.7685 161.721 48.9085 165.85
                    48.9085C168.815 48.9085 171.548 47.2803 172.711 44.78L180.91 49.6645C178.119 55.014 172.363 58.3284 165.559
                    58.3284Z" fill="currentColor"></path><path fillrule="evenodd" clip-rule="evenodd" d="M194.239
                    13.687H183.587V58.0743H194.239V46.7744L195.487 45.6104L205.225 58.0743H217.618L202.656 38.925L216.869
                    25.6715H203.852L194.239 34.6361V13.687Z" fill="currentColor"></path><path fillrule="evenodd" clip-rule="evenodd" d="M219.541 41.3012V13.687H230.639V40.6829C230.639 45.5092 233.451 47.835 237.519 47.835C241.527 47.835
                    244.398 45.3928 244.398 40.6829V13.687H255.495V41.3012C255.495 53.9773 246.596 58.7454 237.519 58.7454C228.5
                    58.7454 219.541 53.9773 219.541 41.3012Z" fill="currentColor"></path><path fillrule="evenodd" clip-rule="evenodd" d="M279.464 58.9619C288.044 58.9619 295 51.4102 295 42.0947C295 32.7792 288.044 25.2275 279.464
                    25.2275C275.967 25.2275 272.74 26.4822 270.143 28.5995V25.6715H259.934V71.3904H270.143V55.5899C272.74
                    57.7072 275.967 58.9619 279.464 58.9619ZM270.143 42.112V42.0773C270.152 37.9178 273.427 34.5489 277.466
                    34.5489C281.512 34.5489 284.791 37.9272 284.791 42.0947C284.791 46.2621 281.512 49.6405 277.466
                    49.6405C273.427 49.6405 270.152 46.2715 270.143 42.112Z" fill="currentColor"></path></svg>
            </div>
            <div class="h-10 w-auto">
                <svg class="w-full h-full" viewBox="0 0 321 83" fill="none" xmlns="http://www.w3.org/2000/svg" id="Windframe_O7GNjUbGPil"><path d="M83.9907 62.856V0.0898438H93.732V24.8368C96.805 20.2715 101.412 17.9888
                    107.552 17.9888C112.791 17.9888 116.84 19.5382 119.699 22.6369C122.558 25.7356 123.984 30.0555 123.979
                    35.5967V62.856H114.246V36.9406C114.246 29.7759 111.055 26.1936 104.672 26.1936C101.479 26.1936 98.8594
                    27.1566 96.8136 29.0826C94.7678 31.0086 93.7448 33.7192 93.7448 37.2146V62.856H83.9907Z" fill="currentColor"></path><path d="M170.087 57.4424C165.753 61.7766 160.305 63.9452 153.742 63.948C147.179 63.9509
                    141.715 61.7824 137.35 57.4424C132.984 53.1082 130.801 47.5984 130.801 40.9131C130.801 34.2277 132.984
                    28.7336 137.35 24.4308C141.715 20.128 147.179 17.9766 153.742 17.9766C160.305 17.9766 165.753 20.128 170.087
                    24.4308C174.421 28.7365 176.59 34.2306 176.593 40.9131C176.596 47.5956 174.427 53.1054 170.087
                    57.4424ZM144.484 51.9341C146.801 54.6447 149.887 56.0001 153.742 56.0001C157.597 56.0001 160.683 54.6447 163
                    51.9341C165.328 49.2291 166.492 45.5555 166.492 40.9131C166.492 36.3477 165.318 32.7055 162.97
                    29.9862C160.621 27.267 157.551 25.9117 153.759 25.9202C149.907 25.9202 146.821 27.2599 144.501
                    29.9392C142.182 32.6184 141.023 36.2764 141.026 40.9131C141.026 45.5526 142.185 49.2263 144.501 51.9341" fill="currentColor"></path><path d="M199.166 63.9436C190.195 63.9436 185.709 59.1272 185.709
                    49.4943V27.2768H178.934V19.0592H185.979L188.239
                    6.14648H195.553V19.0592H212.986V27.2768H195.553V49.2247C195.553 53.2564 197.028 55.2738 199.979
                    55.2766C202.746 55.2766 205.245 52.7471 207.473 47.6882L215.061 51.5744C211.329 59.8234 206.031 63.9465
                    199.166 63.9436Z" fill="currentColor"></path><path d="M218.199 82.9119C213.865 82.9119 210.282 81.7078 207.452
                    79.2996L211.064 71.6213C212.81 73.6671 214.706 74.6915 216.752 74.6943C219.46 74.6943 220.815 72.9481
                    220.818 69.4556V19.0586H230.568V69.6354C230.568 74.3319 229.409 77.7174 227.093 79.7918C224.776 81.8662
                    221.811 82.9062 218.199 82.9119ZM225.693 11.6499C224.09 11.7037 222.531 11.1205 221.357 10.0278C220.794
                    9.49571 220.35 8.8501 220.055 8.13368C219.76 7.41726 219.62 6.64634 219.645 5.87192C219.623 5.09147 219.764
                    4.31493 220.058 3.59195C220.353 2.86897 220.796 2.21546 221.357 1.67324C222.536 0.597131 224.074 0.000488281
                    225.669 0.000488281C227.265 0.000488281 228.803 0.597131 229.982 1.67324C230.558 2.20697 231.013 2.85785
                    231.317 3.58213C231.618 4.30698 231.764 5.08709 231.745 5.87192C231.758 6.64251 231.608 7.40714 231.304
                    8.11538C231 8.82361 230.549 9.45937 229.982 9.98072C229.413 10.5268 228.742 10.9554 228.008 11.2418C227.273
                    11.5282 226.49 11.6669 225.702 11.6499" fill="currentColor"></path><path d="M254.793 63.9435C249.794 63.9435
                    245.745 62.6951 242.646 60.1985C239.547 57.7018 237.997 54.3463 237.994 50.1319C237.994 46.277 239.604
                    43.0085 242.826 40.3264C246.047 37.6443 250.096 36.3089 254.972 36.3203C259.971 36.3203 264.186 37.9154
                    267.616 41.1054V35.7682C267.616 32.6296 266.818 30.2042 265.223 28.4922C263.628 26.7802 261.355 25.9242
                    258.405 25.9242C252.444 25.9242 248.735 28.9644 247.277 35.0449L238.332 33.1489C239.536 28.4551 241.916
                    24.7458 245.471 22.0209C249.026 19.2959 253.449 17.942 258.739 17.9591C264.52 17.9591 269.085 19.4785
                    272.435 22.5173C275.785 25.5561 277.457 29.9974 277.451 35.841V62.8563H267.979V57.3523C264.786 61.7493
                    260.391 63.9463 254.793 63.9435ZM256.599 56.7188C260.933 56.7188 264.605 54.5517 267.616
                    50.2175V48.8607C264.608 45.2484 260.936 43.4423 256.599 43.4423C254.068 43.4423 252.019 44.06 250.453
                    45.2955C249.712 45.8419 249.112 46.5572 248.703 47.382C248.295 48.2068 248.089 49.1173 248.103
                    50.0377C248.079 50.968 248.276 51.8907 248.677 52.7305C249.078 53.5702 249.672 54.3033 250.41
                    54.8699C251.945 56.1025 254.008 56.7188 256.599 56.7188Z" fill="currentColor"></path><path d="M286.953
                    62.8562V19.059H296.437V26.6432C298.831 21.1106 302.985 18.3442 308.9 18.3442C312.401 18.3442 315.293 19.4856
                    317.576 21.7682C319.859 24.0509 321 27.1211 321 30.9788C320.975 33.2855 320.579 35.5732 319.827
                    37.754L310.886 36.851C311.07 35.6541 311.19 34.4483 311.246 33.2386C311.246 28.7247 309.38 26.4663 305.648
                    26.4634C303.119 26.4634 300.997 27.803 299.279 30.4823C297.561 33.1616 296.705 36.6997 296.711
                    41.0967V62.8562H286.953Z" fill="currentColor"></path><path d="M23.0778 25.7611C30.3538 21.7207 40.3005 16.191
                    40.3005 0.0810547H28.248C28.248 9.06905 23.861 11.5258 17.2227 15.2151C9.94672 19.2555 0 24.7809 0
                    40.8951H12.0525C12.0525 31.9071 16.4395 29.4504 23.0778 25.7653" fill="currentColor"></path><path d="M43.9384
                    22.0591C43.9384 31.0685 39.5557 33.5038 32.9174 37.1932C25.6414 41.2292 15.6947 46.759 15.6947
                    62.8732H27.7472C27.7472 53.8638 32.1342 51.4284 38.7725 47.7434C46.0485 43.703 55.9952 38.1733 55.9952
                    22.0634L43.9384 22.0591Z" fill="currentColor"></path></svg>
            </div>
            <div class="h-10 w-auto">
                <svg class="w-full h-full" viewBox="0 0 282 83" fill="none" xmlns="http://www.w3.org/2000/svg" id="Windframe_QiQqltsx1aM"><path d="M189.365 80.9415H181.917C180.404 80.9415 179.244 80.1277 179.13
                    78.6173L179.012 75.7052C173.656 80.0163 167.256 82.3397 160.507 82.3397C147.476 82.3397 142.126 74.0841
                    142.126 61.5108V27.094C142.126 25.1147 143.289 24.4159 145.144 24.4159H153.294C155.157 24.4159 156.206
                    24.9948 156.206 27.094V59.0654C156.206 65.468 157.834 69.8871 164.818 69.8871C169.233 69.8871 174.823
                    67.4466 178.314 64.3046V27.094C178.314 25.1147 179.363 24.4159 181.219 24.4159H189.481C191.345 24.4159
                    192.395 24.9948 192.395 27.094V77.6859C192.393 80.1277 191.69 80.9415 189.365 80.9415ZM0 26.7372C0 25.3446
                    0.699492 24.4131 2.09354 24.4131H11.9372C13.3326 24.4131 14.1471 25.2282 14.1471 26.7372V78.0328C14.1471
                    80.1277 13.2121 80.9415 11.2384 80.9415H3.02643C0.938531 80.9415 0 80.1277 0 78.0328V26.7372ZM206.952
                    26.7372C206.952 25.3446 207.655 24.4131 209.048 24.4131H218.891C220.289 24.4131 221.103 25.2282 221.103
                    26.7372V78.0328C221.103 80.1277 220.171 80.9415 218.191 80.9415H209.979C207.89 80.9415 206.952 80.1277
                    206.952 78.0328V26.7372ZM76.521 80.9415H68.144C66.0505 80.9415 65.3531 79.8958 65.3531
                    77.915V46.2638C65.3531 39.8675 63.6107 35.3279 56.5086 35.3279C51.969 35.3279 46.3864 37.7761 42.7797
                    40.9132V78.0321C42.7797 80.0156 41.9646 80.9408 39.9902 80.9408H31.4926C29.7467 80.9408 28.6982 80.0156
                    28.6982 78.1491V27.3359C28.6982 25.4729 29.5183 24.4272 31.6097 24.4272H39.1694C40.7976 24.4272 41.851
                    24.8905 41.9639 26.6357L42.0788 29.7418C47.4357 25.4398 54.1845 22.9979 60.934 22.9979C73.9663 22.9979
                    79.3175 31.1415 79.3175 43.8247V77.915C79.3168 79.8958 78.6209 80.9415 76.521 80.9415ZM117.992
                    8.53563C117.992 3.82182 114.174 0 109.459 0C104.747 0 100.924 3.82182 100.924 8.53563C100.924 13.2494
                    104.748 17.067 109.459 17.067C114.174 17.067 117.992 13.2494 117.992 8.53563ZM130.136 24.4131C131.532
                    24.4131 132.347 24.8764 132.347 26.391V34.5853C132.347 35.9808 131.418 36.7959 130.019
                    36.7959H116.533V78.0321C116.533 80.127 115.603 80.9408 113.507 80.9408H105.41C103.315 80.9408 102.386 80.127
                    102.386 78.0321V36.7959H88.8968C87.5013 36.7959 86.5705 35.9808 86.5705 34.5853V26.391C86.5705 24.8764
                    87.3843 24.4131 88.7818 24.4131H130.136ZM262.39 8.53563C262.39 3.82182 258.568 0 253.854 0C249.142 0 245.321
                    3.82182 245.321 8.53563C245.321 13.2494 249.142 17.067 253.854 17.067C258.568 17.067 262.39 13.2494 262.39
                    8.53563ZM274.534 24.4131C275.926 24.4131 276.74 24.8764 276.74 26.391V34.5853C276.74 35.9808 275.813 36.7959
                    274.412 36.7959H260.927V78.0321C260.927 80.127 259.999 80.9408 257.903 80.9408H249.804C247.708 80.9408
                    246.78 80.127 246.78 78.0321L246.777 36.7959H233.295C231.896 36.7959 230.965 35.9808 230.965
                    34.5853V26.391C230.965 24.8764 231.782 24.4131 233.174 24.4131H274.534ZM278.221 73.7696C280.278 73.7696
                    281.998 75.3907 281.998 77.5173C281.998 79.6814 280.278 81.3067 278.221 81.3067C276.155 81.3067 274.422
                    79.6814 274.422 77.5173C274.423 75.3907 276.155 73.7696 278.221 73.7696ZM278.221 80.7539C279.966 80.7539
                    281.315 79.3641 281.315 77.5173C281.315 75.715 279.966 74.3224 278.221 74.3224C276.456 74.3224 275.105
                    75.7157 275.105 77.5173C275.105 79.3648 276.456 80.7539 278.221 80.7539ZM276.75 75.3307H278.451C279.5
                    75.3307 279.995 75.7439 279.995 76.5922C279.995 77.3841 279.493 77.7218 278.835 77.7902L280.096
                    79.7364H279.359L278.161 77.8389H277.434V79.7364H276.749V75.3307H276.75ZM277.434 77.2854H278.151C278.756
                    77.2854 279.309 77.2571 279.309 76.5619C279.309 75.9759 278.807 75.8871 278.343
                    75.8871H277.434V77.2854H277.434Z" fill="currentColor"></path></svg>
            </div>
            <div class="h-10 w-auto">
                <svg class="w-full h-full" viewBox="0 0 326 82" fill="none" xmlns="http://www.w3.org/2000/svg" id="Windframe_kHDVzQhg0wz"><path fillrule="evenodd" clip-rule="evenodd" d="M13.6987
                    5.06878e-07H68.2825C70.0818 -0.000488931 71.8635 0.353474 73.526 1.04167C75.1885 1.72987 76.6991 2.73883
                    77.9715 4.01091C79.244 5.283 80.2534 6.79329 80.9421 8.45554C81.6308 10.1178 81.9853 11.8994 81.9853
                    13.6987V68.2784C81.9853 75.8464 75.8502 81.9812 68.2825 81.9812H13.6987C6.13257 81.979 0 75.8449 0
                    68.2784V13.6987C0 6.13294 6.13294 5.06878e-07 13.6987 5.06878e-07ZM62.7656 67.0774C65.1535 67.0774 67.0893
                    65.1415 67.0893 62.7537L67.0774 19.2313C67.0774 16.8434 65.1415 14.9079 62.7537 14.9079H19.2354C18.6673
                    14.9079 18.1047 15.0199 17.5799 15.2374C17.0551 15.4549 16.5783 15.7737 16.1767 16.1756C15.7752 16.5775
                    15.4568 17.0546 15.2397 17.5796C15.0226 18.1046 14.9111 18.6673 14.9116 19.2354V62.7537C14.9116 65.1415
                    16.8475 67.0774 19.2354 67.0774H62.7656Z" fill="currentColor"></path><path d="M32.294 52.1057C31.6391 52.1006
                    31.0129 51.8369 30.5516 51.372C30.0904 50.9071 29.8316 50.2788 29.8316 49.6239V32.2499C29.83 31.923 29.893
                    31.5989 30.0171 31.2965C30.1411 30.994 30.3236 30.719 30.5542 30.4873C30.7849 30.2555 31.059 30.0717 31.3609
                    29.9462C31.6627 29.8207 31.9864 29.7561 32.3134 29.7562H49.7116C50.0384 29.7568 50.3618 29.8218 50.6634
                    29.9475C50.965 30.0732 51.2389 30.2571 51.4693 30.4887C51.6998 30.7203 51.8824 30.995 52.0067
                    31.2972C52.1309 31.5994 52.1944 31.9231 52.1934 32.2499V49.6198C52.1946 49.9466 52.1312 50.2705 52.0071
                    50.5728C51.8829 50.8751 51.7003 51.1499 51.4698 51.3816C51.2393 51.6132 50.9653 51.7972 50.6637
                    51.9228C50.362 52.0485 50.0385 52.1134 49.7116 52.1139L32.294 52.1057ZM123.702 37.3372C121.164 36.6452
                    118.762 35.9927 116.896 35.1617C113.444 33.6182 111.837 31.4781 111.837 28.4318C111.837 22.6719 117.405
                    20.0786 122.95 20.0786C128.22 20.0786 132.822 22.2586 135.913 26.2123L136.124 26.4825L140.579
                    22.9983L140.364 22.7278C136.255 17.5011 130.17 14.6377 123.22 14.6377C118.595 14.6377 114.355 15.886 111.288
                    18.1573C107.836 20.6831 106.014 24.3469 106.014 28.718C106.014 38.8807 115.584 41.3584 123.276
                    43.3512C131.061 45.4038 135.83 46.9629 135.83 52.8972C135.83 58.7402 131.104 62.5151 123.793 62.5151C120.178
                    62.5151 113.484 61.5567 109.275 55.129L109.081 54.8264L104.395 58.2193L104.578 58.4939C108.556 64.5158
                    115.421 67.9724 123.436 67.9724C134.322 67.9724 141.641 61.8268 141.641 52.6867C141.641 42.2255 131.689
                    39.5168 123.702 37.3372Z" fill="currentColor"></path><path fillrule="evenodd" clip-rule="evenodd" d="M177.153
                    35.2253V29.7244H182.391V81.974H177.153V61.5607C174.162 65.6616 169.675 67.9049 164.401 67.9049C154.441
                    67.9049 147.48 59.93 147.48 48.415C147.48 36.9 154.457 28.8613 164.401 28.8613C169.639 28.8613 174.126
                    31.1166 177.153 35.2253ZM153.001 48.3512C153.001 59.011 159.107 62.8098 164.822 62.8098L164.834
                    62.8135C172.316 62.8135 177.153 57.086 177.153 48.3512C177.153 39.6165 172.308 33.9684 164.822
                    33.9684C156.108 33.9684 153.001 41.3985 153.001 48.3512Z" fill="currentColor"></path><path d="M217.023
                    29.7241V50.3476C217.023 57.5672 212.078 62.8095 205.269 62.8095C199.613 62.8095 196.86 59.4524 196.86
                    52.5514V29.7241H191.622V53.6213C191.622 62.5706 196.486 67.9125 204.632 67.9125C209.708 67.9125 214.067
                    65.7608 217.026 61.8269V67.0614H222.265V29.7241H217.023Z" fill="currentColor"></path><path fillrule="evenodd" clip-rule="evenodd" d="M232.131 33.6458C235.993 30.6114 241.235 28.873 246.505 28.873C254.83 28.873 259.798
                    33.0138 259.782 39.9584V67.0696H254.54V62.9288C251.895 66.242 248.068 67.9207 243.144 67.9207C235.121
                    67.9207 230.134 63.5332 230.134 56.4772C230.134 47.3009 238.781 45.861 242.464 45.2484C243.065 45.1493
                    243.685 45.0539 244.305 44.9581H244.306L244.328 44.9548C249.361 44.1801 254.556 43.3807 254.556
                    39.1114C254.556 34.2782 248.283 33.9048 246.362 33.9048C242.969 33.9048 238.176 34.911 234.919
                    37.7348L234.621 37.9935L231.884 33.8411L232.131 33.6458ZM235.603 56.1829C235.603 62.1652 241.235 62.8133
                    243.657 62.8133H243.661C248.928 62.8133 254.564 60.0054 254.552 52.114V46.7122C251.997 48.3261 248.36
                    48.9566 245.116 49.5189L245.062 49.5286L243.435 49.8189C238.24 50.7814 235.603 52.0223 235.603 56.1829Z" fill="currentColor"></path><path d="M290.131 30.4282C288.886 29.5414 286.925 29.0122 284.88 29.0122C282.808 29.0395
                    280.771 29.5477 278.928 30.4968C277.086 31.4459 275.489 32.81 274.264
                    34.4815V29.7083H269.026V67.0422H274.264V47.0749C274.264 38.2209 279.24 34.2553 284.168 34.2553C285.61 34.236
                    287.039 34.539 288.349 35.1422L288.703 35.3292L290.362 30.5799L290.131 30.4282Z" fill="currentColor"></path><path fillrule="evenodd" clip-rule="evenodd" d="M291.666 48.4941C291.666 36.9434 298.869 28.873 309.167
                    28.873C319.087 28.873 326.02 36.0367 326 46.3105C325.997 47.2034 325.948 48.0956 325.853 48.9834L325.821
                    49.2897H297.182C297.314 57.5113 302.262 62.8133 309.855 62.8133C314.207 62.8133 317.977 61.0436 320.471
                    57.8255L320.698 57.5314L324.492 60.9121L324.282 61.1666C321.76 64.2412 317.162 67.9088 309.565
                    67.9088C298.865 67.9088 291.666 60.1086 291.666 48.4941ZM309.023 33.9007C302.624 33.9007 298.085 38.1127
                    297.377 44.6719H320.463C320.01 39.3976 316.788 33.9007 309.023 33.9007Z" fill="currentColor"></path></svg>
            </div>
            </div>
        </section>
        <section class="py-20 px-4 sm:px-6 lg:px-8">
            <div class="max-w-7xl mx-auto">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl font-bold text-black mb-4">Powerful features for link management</h2>
                    <p class="text-xl text-black max-w-3xl mx-auto">Everything you need to create, track, and optimize your links</p>
                </div>
                
                <!-- URL Shortener Feature -->
                <div class="grid md:grid-cols-2 gap-12 items-center my-40 fade-in" data-aos="fade-up">
                    <div class="order-2 md:order-1">
                        <div class="inline-block px-4 py-1 bg-pink-100 text-pink-600 rounded-full text-sm font-medium mb-4">URL Shortener</div>
                        <h3 class="text-2xl md:text-3xl font-bold text-black mb-4">Create branded short links with powerful analytics</h3>
                        <p class="text-lg text-black mb-6">Transform long, unwieldy links into clean, memorable URLs that reflect your brand. Track clicks, geographic data, and referral sources in real-time.</p>
                        <ul class="space-y-3 mb-8">
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-black">Custom domains and branded links</span>
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-black">Detailed click analytics and insights</span>
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-black">UTM parameter builder and tracking</span>
                            </li>
                        </ul>
                        <a href="#" class="inline-flex items-center text-purple-600 font-medium hover:text-purple-700">
                            Learn more
                            <svg class="w-5 h-5 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                            </svg>
                        </a>
                    </div>
                    <div class="order-1 md:order-2 float">
                        <div class="glass rounded-2xl p-6 shadow-lg">
                            <div class="bg-transparent rounded-xl shadow-sm p-6">
                                <div class="flex items-center space-x-2 mb-4">
                                    <div class="w-3 h-3 rounded-full bg-red-500"></div>
                                    <div class="w-3 h-3 rounded-full bg-yellow-500"></div>
                                    <div class="w-3 h-3 rounded-full bg-green-500"></div>
                                    <div class="ml-auto text-sm text-gray-400">URL Shortener</div>
                                </div>
                                <div class="glass bg-transparent rounded-lg p-4 mb-4">
                                    <div class="flex items-center mb-3">
                                        <span class="text-gray-400 mr-2">https://</span>
                                        <span class="bg-transparent flex-1 text-black">example.com/very/long/url/that/needs/shortening</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-2">
                                            <span class="text-sm text-gray-500">syncly.io/</span>
                                            <span class="bg-transparent text-sm text-black">product-demo</span>
                                        </div>
                                        <button class="bg-purple-600 text-black px-4 py-2 rounded-lg text-sm">
                                            Shorten
                                        </button>
                                    </div>
                                </div>
                                <div class="glass flex items-center justify-between p-3 rounded-lg">
                                    <div>
                                        <div class="text-purple-200 font-medium">syncly.io/product-demo</div>
                                        <div class="text-xs text-gray-200">Created 2 minutes ago • 24 clicks</div>
                                    </div>
                                    <button class="text-gray-400 hover:text-black">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- QR Codes Feature -->
                <div class="grid md:grid-cols-2 gap-12 items-center my-24" data-aos="fade-up">
                    <div class="float">
                        <div class="glass rounded-2xl p-6 shadow-lg">
                            <div class="rounded-xl shadow-sm p-6">
                                <div class="flex items-center space-x-2 mb-4">
                                    <div class="w-3 h-3 rounded-full bg-red-500"></div>
                                    <div class="w-3 h-3 rounded-full bg-yellow-500"></div>
                                    <div class="w-3 h-3 rounded-full bg-green-500"></div>
                                    <div class="ml-auto text-sm text-gray-400">QR Code Generator</div>
                                </div>
                                <div class="glass flex justify-center mb-4">
                                    <div class="glass w-48 h-48  border rounded-lg p-4 flex items-center justify-center">
                                        <svg class="w-full h-full" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <rect x="10" y="10" width="30" height="30" rx="2" fill="#4F46E5" />
                                            <rect x="60" y="10" width="30" height="30" rx="2" fill="#4F46E5" />
                                            <rect x="10" y="60" width="30" height="30" rx="2" fill="#4F46E5" />
                                            <rect x="20" y="20" width="10" height="10" rx="1" fill="white" />
                                            <rect x="70" y="20" width="10" height="10" rx="1" fill="white" />
                                            <rect x="20" y="70" width="10" height="10" rx="1" fill="white" />
                                            <rect x="60" y="60" width="10" height="10" rx="1" fill="#4F46E5" />
                                            <rect x="80" y="60" width="10" height="10" rx="1" fill="#4F46E5" />
                                            <rect x="60" y="80" width="30" height="10" rx="1" fill="#4F46E5" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="flex justify-between">
                                    <button class="bg-gray-100 text-black px-3 py-2 rounded-lg text-sm flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                                        </svg>
                                        Download
                                    </button>
                                    <button class="bg-indigo-600 text-black px-3 py-2 rounded-lg text-sm">
                                        Customize
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="inline-block px-4 py-1 bg-indigo-100 text-indigo-600 rounded-full text-sm font-medium mb-4">QR Codes</div>
                        <h3 class="text-2xl md:text-3xl font-bold text-black mb-4">Generate dynamic QR codes with tracking</h3>
                        <p class="text-lg text-black mb-6">Create customizable QR codes that connect your offline materials to your online presence. Update destinations without reprinting and track scans in real-time.</p>
                        <ul class="space-y-3 mb-8">
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-black">Customizable designs and branding</span>
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-black">Dynamic QR codes with editable destinations</span>
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-black">Scan analytics and location tracking</span>
                            </li>
                        </ul>
                        <a href="#" class="inline-flex items-center text-indigo-600 font-medium hover:text-indigo-700">
                            Learn more
                            <svg class="w-5 h-5 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                            </svg>
                        </a>
                    </div>
                </div>
                
                <!-- Link-in Bio Feature -->
                <div class="grid md:grid-cols-2 gap-12 items-center" data-aos="fade-up">
                    <div class="order-2 md:order-1">
                        <div class="inline-block px-4 py-1 bg-green-100 text-green-600 rounded-full text-sm font-medium mb-4">Link-in Bio</div>
                        <h3 class="text-2xl md:text-3xl font-bold text-black mb-4">Create beautiful bio pages in minutes</h3>
                        <p class="text-lg text-black mb-6">Build a customizable landing page that houses all your important links in one place. Perfect for social media profiles, marketing campaigns, and personal branding.</p>
                        <ul class="space-y-3 mb-8">
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-black">Customizable themes and layouts</span>
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-black">Social media integration</span>
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-black">Visitor analytics and insights</span>
                            </li>
                        </ul>
                        <a href="#" class="inline-flex items-center text-green-600 font-medium hover:text-green-700">
                            Learn more
                            <svg class="w-5 h-5 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                            </svg>
                        </a>
                    </div>
                    <div class="order-1 md:order-2 float">
                        <div class="bg-gradient-to-br from-green-50 to-teal-50 rounded-2xl p-6 shadow-lg">
                            <div class="bg-white rounded-xl shadow-sm p-6">
                                <div class="flex items-center space-x-2 mb-4">
                                    <div class="w-3 h-3 rounded-full bg-red-500"></div>
                                    <div class="w-3 h-3 rounded-full bg-yellow-500"></div>
                                    <div class="w-3 h-3 rounded-full bg-green-500"></div>
                                    <div class="ml-auto text-sm text-gray-400">Bio Page</div>
                                </div>
                                <div class="flex justify-center mb-4">
                                    <div class="w-48 bg-gray-50 rounded-xl overflow-hidden">
                                        <div class="h-16 bg-gradient-to-r from-green-400 to-blue-500"></div>
                                        <div class="p-4 flex flex-col items-center">
                                            <div class="w-16 h-16 bg-white rounded-full border-4 border-white -mt-10 mb-2 overflow-hidden">
                                                <div class="w-full h-full bg-gray-200"></div>
                                            </div>
                                            <h3 class="font-bold text-gray-800">@username</h3>
                                            <p class="text-xs text-gray-500 text-center mt-1 mb-3">Digital creator & content specialist</p>
                                            <div class="space-y-2 w-full">
                                                <div class="bg-white border rounded-lg p-2 text-xs text-center">Latest Video</div>
                                                <div class="bg-white border rounded-lg p-2 text-xs text-center">Shop My Products</div>
                                                <div class="bg-white border rounded-lg p-2 text-xs text-center">Contact Me</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section class="mx-auto py-20 max-w-screen-2xl p-4 relative isolate overflow-hidden">
            <svg class="w-[468px] h-[788px] absolute -top-28 -right-20 -z-10 transform-gpu overflow-hidden blur-2xl" viewBox="0
                0 468 788" fill="none" xmlns="http://www.w3.org/2000/svg" id="Windframe_qU9keQGYTlK"><circle cx="44.5105" cy="378.637" r="156.383" fill="#4A3AFF"></circle><circle cx="119.803" cy="529.24" r="156.383" fill="#702DFF"></circle><circle cx="173.364" cy="372.857" r="156.383" fill="#2D5BFF"></circle><g filter="url(#filter0_b_1410_520)"><circle cx="73.5409" cy="394.049" r="393.819" fill="white" fill-opacity="0.79"></circle></g><defs><filter x="-460.404" y="-139.896" width="1067.89" height="1067.89" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood><feGaussianBlur in="BackgroundImageFix" stdDeviation="70.063"></feGaussianBlur><feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1410_520"></feComposite><feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1410_520" result="shape"></feBlend></filter></defs></svg>
            <p class="text-center text-gray-600 mb-2 font-medium tracking-wide uppercase">Features</p>
            <p class="text-4xl md:text-6xl font-bold tracking-tight text-center">Enjoy the best set of features</p>
            <p class="mt-5 text-gray-600 text-lg text-center md:max-w-xl mx-auto">Lorem ipsum dolor sit amet consectetur
                adipisicing elit. Minima dolore facilis modi, tempore minus et adipisci sapiente consequaturl.</p>
            <div class="md:grid-cols-2 xl:grid-cols-3 mx-auto mt-10 grid gap-10">
            <div class="rounded-xl justify-center items-center mx-auto px-10 py-24 bg-white border border-gray-300 flex
                flex-col gap-4 max-w-[30rem]">
                <div class="w-16 h-16 bg-white rounded-full text-gray-600 p-3 border">
                <svg class="w-full h-full" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" id="Windframe_L9yGyU1Pdbm"><g id="Calendar / Calendar_Check"><path id="Vector" d="M4 8H20M4 8V16.8002C4
                    17.9203 4 18.4801 4.21799 18.9079C4.40973 19.2842 4.71547 19.5905 5.0918 19.7822C5.5192 20 6.07899 20
                    7.19691 20H16.8031C17.921 20 18.48 20 18.9074 19.7822C19.2837 19.5905 19.5905 19.2842 19.7822 18.9079C20
                    18.4805 20 17.9215 20 16.8036V8M4 8V7.2002C4 6.08009 4 5.51962 4.21799 5.0918C4.40973 4.71547 4.71547
                    4.40973 5.0918 4.21799C5.51962 4 6.08009 4 7.2002 4H8M20 8V7.19691C20 6.07899 20 5.5192 19.7822
                    5.0918C19.5905 4.71547 19.2837 4.40973 18.9074 4.21799C18.4796 4 17.9203 4 16.8002 4H16M8 4H16M8 4V2M16
                    4V2M15 12L11 16L9 14" stroke="currentColor" strokewidth="1" strokelinecap="round" strokelinejoin="round"></path></g></svg>
                </div>
                <p class="text-3xl font-medium text-center">Work management</p>
                <p class="text-center text-gray-600">Lorem ipsum dolor, sit amet consectetur adipisicing elit. Perferendis
                    debitis, ex laboriosam, vel maiores neque quasi alias consectetur .</p>
            </div>
            <div class="rounded-xl justify-center items-center mx-auto py-24 bg-white p-10 border border-gray-300 flex
                flex-col gap-4 max-w-[30rem]">
                <div class="w-16 h-16 bg-white rounded-full text-gray-600 p-3 border">
                <svg class="w-full h-full" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" id="Windframe_bv_ocPoqx0B"><path d="M19.902 13.161a7.876 7.876 0 0 0-3.956-8.1c0-.021.006-.04.006-.061a3.952
                    3.952 0 0 0-7.904 0c0 .*************.06a7.876 7.876 0 0 0-3.956 8.101 3.946 3.946 0 1 0 4.242 5.93 7.855
                    7.855 0 0 0 7.32 0 3.945 3.945 0 1 0 4.242-5.93zM12 2.051A2.948 2.948 0 1 1 9.052 5 2.951 2.951 0 0 1 12
                    2.052zM5 19.949A2.948 2.948 0 1 1 7.948 17 2.951 2.951 0 0 1 5 19.948zm3.75-1.76A3.896 3.896 0 0 0 8.952
                    17a3.952 3.952 0 0 0-3.868-3.944A7.1 7.1 0 0 1 4.996 12a6.977 6.977 0 0 1 3.232-5.885 3.926 3.926 0 0 0
                    7.544 0A6.977 6.977 0 0 1 19.004 12a7.1 7.1 0 0 1-.088 1.056A3.952 3.952 0 0 0 15.048 17a3.896 3.896 0 0 0
                    .202 1.188 7.13 7.13 0 0 1-6.5 0zM19 19.948A2.948 2.948 0 1 1 21.948 17 2.951 2.951 0 0 1 19
                    19.948z"></path><path fill="none" d="M0 0h24v24H0z"></path></svg>
                </div>
                <p class="text-3xl font-medium text-center">Collaboration</p>
                <p class="text-center text-gray-600">Lorem ipsum dolor, sit amet consectetur adipisicing elit. Perferendis
                    debitis, ex laboriosam, vel maiores neque quasi alias consectetur .</p>
            </div>
            <div class="rounded-xl justify-center items-center mx-auto py-24 bg-white p-10 border border-gray-300 flex
                flex-col gap-4 max-w-[30rem]">
                <div class="w-16 h-16 bg-white rounded-full text-gray-600 p-3 border">
                <svg class="w-full h-full" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" id="Windframe_TAkkZPh6-6R"><circle cx="12" cy="12" r="3" stroke="currentColor" strokewidth="1" strokelinecap="round" strokelinejoin="round"></circle><circle cx="12" cy="4" r="2" stroke="currentColor" strokewidth="1" strokelinecap="round" strokelinejoin="round"></circle><circle cx="20" cy="12" r="2" stroke="currentColor" strokewidth="1" strokelinecap="round" strokelinejoin="round"></circle><circle cx="4" cy="12" r="2" stroke="currentColor" strokewidth="1" strokelinecap="round" strokelinejoin="round"></circle><circle cx="12" cy="20" r="2" stroke="currentColor" strokewidth="1" strokelinecap="round" strokelinejoin="round"></circle><path d="M12 6V9" stroke="currentColor" strokewidth="1" strokelinecap="round" strokelinejoin="round"></path><path d="M15 12H18" stroke="currentColor" strokewidth="1" strokelinecap="round" strokelinejoin="round"></path><path d="M12 15V18" stroke="currentColor" strokewidth="1" strokelinecap="round" strokelinejoin="round"></path><path d="M9 12H6" stroke="currentColor" strokewidth="1" strokelinecap="round" strokelinejoin="round"></path></svg>
                </div>
                <p class="text-3xl font-medium text-center">Apps Integration</p>
                <p class="text-center text-gray-600">Lorem ipsum dolor, sit amet consectetur adipisicing elit. Perferendis
                    debitis, ex laboriosam, vel maiores neque quasi alias consectetur .</p>
            </div>
            <div class="rounded-xl justify-center items-center mx-auto py-24 bg-white p-10 border border-gray-300 flex
                flex-col gap-4 max-w-[30rem]">
                <div class="w-16 h-16 bg-white rounded-full text-gray-600 p-3 border">
                <svg fill="currentColor" class="w-full h-full" version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0
                    511 511" id="Windframe_xQdpxs1_TbR"><g><path d="M415.5,0h-352C54.953,0,48,6.953,48,15.5V32H31.5C22.953,32,16,38.953,16,47.5v448c0,8.547,6.953,15.5,15.5,15.5h352 c8.547,0,15.5-6.953,15.5-15.5V479h16.5c8.547,0,15.5-6.953,15.5-15.5v-448C431,6.953,424.047,0,415.5,0z M384,471.423 c0,0.026-0.004,0.051-0.004,0.077s0.004,0.051,0.004,0.077V495.5c0,0.276-0.224,0.5-0.5,0.5h-352c-0.276,0-0.5-0.224-0.5-0.5v-448 c0-0.276,0.224-0.5,0.5-0.5h23.971c0.01,0,0.019,0.001,0.029,0.001S55.519,47,55.529,47H383.5c0.276,0,0.5,0.224,0.5,0.5V471.423z M416,463.5c0,0.276-0.224,0.5-0.5,0.5H399V47.5c0-8.547-6.953-15.5-15.5-15.5H63V15.5c0-0.276,0.224-0.5,0.5-0.5h352 c0.276,0,0.5,0.224,0.5,0.5V463.5z"></path><path d="M335,104H79c-4.142,0-7.5,3.358-7.5,7.5s3.358,7.5,7.5,7.5h256c4.142,0,7.5-3.358,7.5-7.5S339.142,104,335,104z"></path><path d="M335,200H79c-4.142,0-7.5,3.358-7.5,7.5s3.358,7.5,7.5,7.5h256c4.142,0,7.5-3.358,7.5-7.5S339.142,200,335,200z"></path><path d="M335,424H79c-4.142,0-7.5,3.358-7.5,7.5s3.358,7.5,7.5,7.5h256c4.142,0,7.5-3.358,7.5-7.5S339.142,424,335,424z"></path><path d="M335,296H79c-4.142,0-7.5,3.358-7.5,7.5s3.358,7.5,7.5,7.5h256c4.142,0,7.5-3.358,7.5-7.5S339.142,296,335,296z"></path><path d="M79,151h64c4.142,0,7.5-3.358,7.5-7.5s-3.358-7.5-7.5-7.5H79c-4.142,0-7.5,3.358-7.5,7.5S74.858,151,79,151z"></path><path d="m335,136h-160c-4.142,0-7.5,3.358-7.5,7.5s3.358,7.5 7.5,7.5h160c4.142,0 7.5-3.358 7.5-7.5s-3.358-7.5-7.5-7.5z"></path><path d="M79,183h192c4.142,0,7.5-3.358,7.5-7.5s-3.358-7.5-7.5-7.5H79c-4.142,0-7.5,3.358-7.5,7.5S74.858,183,79,183z"></path><path d="m335,168h-32c-4.142,0-7.5,3.358-7.5,7.5s3.358,7.5 7.5,7.5h32c4.142,0 7.5-3.358 7.5-7.5s-3.358-7.5-7.5-7.5z"></path><path d="M335,392H79c-4.142,0-7.5,3.358-7.5,7.5s3.358,7.5,7.5,7.5h256c4.142,0,7.5-3.358,7.5-7.5S339.142,392,335,392z"></path><path d="M79,343h64c4.142,0,7.5-3.358,7.5-7.5s-3.358-7.5-7.5-7.5H79c-4.142,0-7.5,3.358-7.5,7.5S74.858,343,79,343z"></path><path d="m335,328h-160c-4.142,0-7.5,3.358-7.5,7.5s3.358,7.5 7.5,7.5h160c4.142,0 7.5-3.358 7.5-7.5s-3.358-7.5-7.5-7.5z"></path><path d="M79,375h192c4.142,0,7.5-3.358,7.5-7.5s-3.358-7.5-7.5-7.5H79c-4.142,0-7.5,3.358-7.5,7.5S74.858,375,79,375z"></path><path d="m335,360h-32c-4.142,0-7.5,3.358-7.5,7.5s3.358,7.5 7.5,7.5h32c4.142,0 7.5-3.358 7.5-7.5s-3.358-7.5-7.5-7.5z"></path><path d="M79,247h104c4.142,0,7.5-3.358,7.5-7.5s-3.358-7.5-7.5-7.5H79c-4.142,0-7.5,3.358-7.5,7.5S74.858,247,79,247z"></path><path d="m335,232h-120c-4.142,0-7.5,3.358-7.5,7.5s3.358,7.5 7.5,7.5h120c4.142,0 7.5-3.358 7.5-7.5s-3.358-7.5-7.5-7.5z"></path><path d="M79,279h168c4.142,0,7.5-3.358,7.5-7.5s-3.358-7.5-7.5-7.5H79c-4.142,0-7.5,3.358-7.5,7.5S74.858,279,79,279z"></path><path d="m335,264h-56c-4.142,0-7.5,3.358-7.5,7.5s3.358,7.5 7.5,7.5h56c4.142,0 7.5-3.358 7.5-7.5s-3.358-7.5-7.5-7.5z"></path><path d="m471.5,144c-12.958,0-23.5,10.542-23.5,23.5v280c0,0.806 0.13,1.607 0.385,2.372l15.615,46.845v6.783c0,4.142 3.358,7.5 7.5,7.5s7.5-3.358 7.5-7.5v-6.783l15.615-46.846c0.255-0.765 0.385-1.565 0.385-2.372v-280c0-12.957-10.542-23.499-23.5-23.499zm0,327.783l-8.261-24.783h16.522l-8.261,24.783zm8.5-39.783h-17v-241h17v241zm0-256h-17v-8.5c0-4.687 3.813-8.5 8.5-8.5s8.5,3.813 8.5,8.5v8.5z"></path></g></svg>
                </div>
                <p class="text-3xl font-medium text-center">Documentation</p>
                <p class="text-center text-gray-600">Lorem ipsum dolor, sit amet consectetur adipisicing elit. Perferendis
                    debitis, ex laboriosam, vel maiores neque quasi alias consectetur .</p>
            </div>
            <div class="rounded-xl justify-center items-center mx-auto py-24 bg-white p-10 border border-gray-300 flex
                flex-col gap-4 max-w-[30rem]">
                <div class="w-16 h-16 bg-white rounded-full text-gray-600 p-3 border">
                <svg fill="currentColor" class="w-full h-full" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" id="Windframe_HuqNhSygkA_"><path d="M10,2C9.4,2,9,2.4,9,3c0,0,0,0,0,0v18c0,0.6,0.4,1,1,1s1-0.4,1-1V3C11,2.4,10.6,2,10,2C10,2,10,2,10,2z
                    M5,12c-0.6,0-1,0.4-1,1c0,0,0,0,0,0v8c0,0.6,0.4,1,1,1s1-0.4,1-1v-8C6,12.4,5.6,12,5,12C5,12,5,12,5,12z
                    M15,8c-0.6,0-1,0.4-1,1c0,0,0,0,0,0v12c0,0.6,0.4,1,1,1s1-0.4,1-1V9C16,8.4,15.6,8,15,8C15,8,15,8,15,8z
                    M20,16c-0.6,0-1,0.4-1,1c0,0,0,0,0,0v4c0,0.6,0.4,1,1,1s1-0.4,1-1v-4C21,16.4,20.6,16,20,16C20,16,20,16,20,16z"></path></svg>
                </div>
                <p class="text-3xl font-medium text-center">Analytics</p>
                <p class="text-center text-gray-600">Lorem ipsum dolor, sit amet consectetur adipisicing elit. Perferendis
                    debitis, ex laboriosam, vel maiores neque quasi alias consectetur .</p>
            </div>
            <div class="rounded-xl justify-center items-center mx-auto py-24 bg-white p-10 border border-gray-300 flex
                flex-col gap-4 max-w-[30rem]">
                <div class="w-16 h-16 bg-white rounded-full text-gray-600 p-3 border">
                <svg fill="currentColor" class="w-full h-full" viewBox="0 0 36 36" version="1.1" xmlns="http://www.w3.org/2000/svg" id="Windframe_eEeYFdQB5Rv"><rect class="clr-i-outline
                    clr-i-outline-path-1" x="6" y="22" width="24" height="2"></rect><path class="clr-i-outline clr-i-outline-path-2" d="M30.84,13.37A1.94,1.94,0,0,0,28.93,12H26.55a3,3,0,0,1-.14,2h2.54C30,16.94,31.72,21.65,32,22.48V30H4V22.48C4.28,21.65,7.05,14,7.05,14H9.58a3,3,0,0,1-.14-2H7.07a1.92,1.92,0,0,0-1.9,1.32C2,22,2,22.1,2,22.33V30a2,2,0,0,0,2,2H32a2,2,0,0,0,2-2V22.33C34,22.1,34,22,30.84,13.37Z"></path><path class="clr-i-outline clr-i-outline-path-3" d="M18,19.84l6.38-6.35A1,1,0,1,0,23,12.08L19,16V4a1,1,0,1,0-2,0V16l-4-3.95a1,1,0,0,0-1.41,1.42Z"></path><rect x="0" y="0" width="36" height="36" fill-opacity="0"></rect></svg>
                </div>
                <p class="text-3xl font-medium text-center">Data Backup</p>
                <p class="text-center text-gray-600">Lorem ipsum dolor, sit amet consectetur adipisicing elit. Perferendis
                    debitis, ex laboriosam, vel maiores neque quasi alias consectetur .</p>
            </div>
            </div>
        </section>
        <section class="mx-auto py-20 max-w-screen-2xl p-4">
            <p class="mb-2 text-gray-600 font-medium tracking-wide text-center lg:text-left uppercase">content</p>
            <div class="mb-4 lg:mb-8 lg:gap-y-0 lg:flex-row lg:justify-between justify-center items-center flex flex-col gap-x-0
                gap-y-6 max-md:max-w-lg max-md:mx-auto">
            <div class="w-full text-center lg:text-left lg:w-2/4">
                <p class="text-4xl lg:text-6xl lg:mb-6 lg:max-w-2xl lg:mx-0 font-bold tracking-tight leading-[3.25rem] mx-auto
                    max-w-max">Increased productivity with our product</p>
            </div>
            <div class="w-full text-center lg:text-left lg:w-2/4">
                <p class="text-lg font-normal text-gray-600 mb-5">We provide all the advantages that can simplify all your
                    workflows without any further requirements and make life easier</p>
                <a href="#" class="items-center justify-center text-base font-semibold text-black lg:justify-start flex flex-row
                    gap-2">
                Get Started
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" class="" id="Windframe_ZAQ-GU301QI"><path d="M7.5 15L11.0858 11.4142C11.7525 10.7475 12.0858 10.4142 12.0858
                    10C12.0858 9.58579 11.7525 9.25245 11.0858 8.58579L7.5 5" stroke="currentColor" strokewidth="1" strokelinecap="round" strokelinejoin="round"></path></svg>
                </a>
            </div>
            </div>
            <div class="relative">
            <img alt="content image" src="https://images.unsplash.com/photo-1525130413817-d45c1d127c42?q=80&amp;w=2940&amp;auto=format&amp;fit=crop&amp;ixlib=rb-4.0.3&amp;ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" class="max-h-[540px] object-cover object-top grayscale w-full">
            <a href="#" aria-label="Play Video" class="items-center justify-center w-full h-full bg-gray-900 flex absolute
                inset-0 transition-colors duration-300 bg-opacity-50 group hover:bg-opacity-25">
                <div class="items-center justify-center w-32 h-32 bg-gray-100 rounded-full shadow-2xl flex transition
                    duration-300 transform group-hover:scale-110">
                <span class="items-center justify-center w-20 text-gray-900 flex">
                    <svg class="w-full h-full" fill="currentColor" viewBox="0 0 24 24"><path d="M16.53,11.152l-8-5C8.221,5.958,7.833,5.949,7.515,6.125C7.197,6.302,7,6.636,7,7v10
                        c0,0.364,0.197,0.698,0.515,0.875C7.667,17.958,7.833,18,8,18c0.184,0,0.368-0.051,0.53-0.152l8-5C16.822,12.665,17,12.345,17,12 S16.822,11.335,16.53,11.152z"></path></svg>
                </span>
                </div>
            </a>
            </div>
        </section>
        <section class="bg-white text-black my-20 mx-auto max-w-screen-2xl p-6 relative isolate overflow-hidden">
            <svg class="h-[610px] w-[200px] absolute -z-10 transform-gpu overflow-hidden blur-2xl -top-32 right-0" viewBox="0 0
                468 788" fill="none" xmlns="http://www.w3.org/2000/svg" id="Windframe_r3bC5jhLo0F"><circle cx="44.5105" cy="378.637" r="156.383" fill="#4A3AFF"></circle><circle cx="119.803" cy="529.24" r="156.383" fill="#702DFF"></circle><circle cx="173.364" cy="372.857" r="156.383" fill="#2D5BFF"></circle><g filter="url(#filter0_b_1410_520)"><circle cx="73.5409" cy="394.049" r="393.819" fill="white" fill-opacity="0.79"></circle></g><defs><filter x="-460.404" y="-139.896" width="1067.89" height="1067.89" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood><feGaussianBlur in="BackgroundImageFix" stdDeviation="70.063"></feGaussianBlur><feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1410_520"></feComposite><feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1410_520" result="shape"></feBlend></filter></defs></svg>
            <p class="tracking-widest text-sm font-bold text-gray-600 text-center uppercase">Stats</p>
            <p class="text-4xl lg:text-6xl font-bold tracking-tight text-center">Our results in numbers</p>
            <div class="lg:grid-cols-3 mt-10 mx-auto grid place-items-center gap-20 max-w-screen-2xl">
            <div class="w-full rounded-xl shadow-lg p-12 space-y-3 border border-gray-100">
                <p class="font-bold text-5xl">99%</p>
                <p class="font-bold text-2xl pt-2">Customer satisfaction</p>
                <p class="text-gray-500">Lorem ipsum dolor, sit amet consectetur adipisicing elit. Dolorum similique magnam
                    amet</p>
            </div>
            <div class="w-full rounded-xl shadow-lg p-12 space-y-3 border border-gray-100">
                <p class="font-bold text-5xl">32M</p>
                <p class="font-medium text-2xl pt-2">Active users</p>
                <p class="text-gray-500">Lorem ipsum dolor, sit amet consectetur adipisicing elit. Dolorum similique magnam
                    amet</p>
            </div>
            <div class="w-full rounded-xl shadow-lg p-12 space-y-3 border border-gray-100">
                <p class="font-bold text-5xl">300%</p>
                <p class="font-medium text-2xl pt-2">Company growth</p>
                <p class="text-gray-500">Lorem ipsum dolor, sit amet consectetur adipisicing elit. Dolorum similique magnam
                    amet</p>
            </div>
            </div>
        </section>
        <section class="mx-auto py-20 px-4 max-w-screen-2xl">
            <p class="tracking-widest text-sm font-bold text-gray-600 text-center uppercase">blog</p>
            <p class="mt-2 mb-14 text-center text-4xl md:text-6xl font-bold text-black tracking-tight">Our popular blogs</p>
            <div class="mb-14 justify-center md:flex-wrap lg:flex-row lg:flex-nowrap lg:justify-between lg:gap-x-8 lg:gap-y-0
                flex flex-wrap gap-y-8">
            <div class="w-full rounded-2xl lg:w-1/3 group cursor-pointer border border-gray-300 p-5 transition-all
                duration-300 hover:border-black max-lg:max-w-xl">
                <div class="mb-6 items-center flex">
                <img alt="Harsh image" src="https://images.pexels.com/photos/4256211/pexels-photo-4256211.jpeg?auto=compress&amp;cs=tinysrgb&amp;dpr=2&amp;w=500" class="w-full rounded-lg">
                </div>
                <div class="block">
                <span class="mb-3 font-medium text-black block">Jan 01, 2024</span>
                <p class="mb-5 text-xl font-medium leading-8 text-gray-900 line-clamp-2">Minimal is bliss</p>
                <p class="mb-10 leading-6 text-gray-500 line-clamp-2">Discover cool strategies to streamline and organize your
                    workflow...</p>
                <a href="#" class="text-lg font-semibold text-black cursor-pointer">Read more...</a>
                </div>
            </div>
            <div class="w-full rounded-2xl lg:w-1/3 group cursor-pointer border border-gray-300 p-5 transition-all
                duration-300 hover:border-black max-lg:max-w-xl">
                <div class="mb-6 items-center flex">
                <img alt="Harsh image" src="https://images.unsplash.com/photo-1557425631-f132f06f4aa1?q=80&amp;w=2692&amp;auto=format&amp;fit=crop&amp;ixlib=rb-4.0.3&amp;ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" class="w-full rounded-lg">
                </div>
                <div class="block">
                <span class="mb-3 font-medium text-black block">Jan 01, 2024</span>
                <p class="mb-5 text-xl font-medium leading-8 text-gray-900 line-clamp-2">Effective documentation</p>
                <p class="mb-10 leading-6 text-gray-500 line-clamp-2">Learn how to use documentation to improve your
                    workflow...</p>
                <a href="#" class="text-lg font-semibold text-black cursor-pointer">Read more...</a>
                </div>
            </div>
            <div class="w-full rounded-2xl lg:w-1/3 group cursor-pointer border border-gray-300 p-5 transition-all
                duration-300 hover:border-black max-lg:max-w-xl">
                <div class="mb-6 items-center flex">
                <img alt="Harsh image" src="https://images.unsplash.com/photo-1543269865-4430f94492b9?q=80&amp;w=2940&amp;auto=format&amp;fit=crop&amp;ixlib=rb-4.0.3&amp;ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" class="w-full rounded-lg">
                </div>
                <div class="block">
                <span class="mb-3 font-medium text-black block">Jan 01, 2024</span>
                <p class="mb-5 text-xl font-medium leading-8 text-gray-900 line-clamp-2">Improve your day to the max</p>
                <p class="mb-10 leading-6 text-gray-500 line-clamp-2">Explore productivity hacks and conquer the world...</p>
                <a href="#" class="text-lg font-semibold text-black cursor-pointer">Read more...</a>
                </div>
            </div>
            </div>
            <div class="items-center justify-center flex">
            <a href="#" class="h-fit w-fit items-center font-medium flex gap-2">
                <span>View all</span>
                <svg class="h-4 w-4" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" id="Windframe_8UEL3OqpFDm"><path d="M7.5 15L11.0858 11.4142C11.7525 10.7475 12.0858 10.4142 12.0858 10C12.0858
                    9.58579 11.7525 9.25245 11.0858 8.58579L7.5 5" stroke="currentColor" strokewidth="1" strokelinecap="round" strokelinejoin="round"></path></svg>
            </a>
            </div>
        </section>
        <section class="bg-white py-20 sm:py-32">
            <div class="mx-auto px-4 max-w-screen-2xl relative isolate">
            <svg class="h-[788px] w-[350px] absolute top-0 -z-10 transform-gpu overflow-hidden blur-2xl left-12" viewBox="0 0
                468 788" fill="none" xmlns="http://www.w3.org/2000/svg" id="Windframe_tfVNzYZuELR"><circle cx="44.5105" cy="378.637" r="156.383" fill="#4A3AFF"></circle><circle cx="119.803" cy="529.24" r="156.383" fill="#702DFF"></circle><circle cx="173.364" cy="372.857" r="156.383" fill="#2D5BFF"></circle><g filter="url(#filter0_b_1410_520)"><circle cx="73.5409" cy="394.049" r="393.819" fill="white" fill-opacity="0.79"></circle></g><defs><filter x="-460.404" y="-139.896" width="1067.89" height="1067.89" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood><feGaussianBlur in="BackgroundImageFix" stdDeviation="70.063"></feGaussianBlur><feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1410_520"></feComposite><feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1410_520" result="shape"></feBlend></filter></defs></svg>
            <div class="mx-auto lg:max-w-4xl text-center">
                <p class="tracking-widest text-sm font-bold text-gray-600 text-center uppercase">timeline</p>
                <p class="mt-2 text-4xl lg:text-6xl font-bold tracking-tight text-center">Product Timeline</p>
                <p class="mt-6 text-lg leading-8 text-gray-600">Quis tellus eget adipiscing convallis sit sit eget aliquet quis.
                    Suspendisse eget egestas a Elementum pulvinar et feugiat blandit at. In mi viverra elit nunc.</p>
            </div>
            <div class="mx-auto mt-16 sm:mt-20 lg:mt-24 lg:max-w-4xl">
                <dl class="lg:max-w-none md:grid-cols-2 md:gap-y-16 grid grid-cols-1 gap-x-8 gap-y-10">
                <div class="sm:flex-row items-center relative flex flex-col gap-6">
                    <div class="h-24 w-24 items-center justify-center rounded-full bg-white text-4xl font-bold flex border
                        border-gray-500">1</div>
                    <div class="w-full flex-1 flex-shrink-0">
                    <p class="mb-1.5 text-xs text-center sm:text-left text-black font-semibold">May 2024</p>
                    <dt class="text-xl text-center sm:text-left font-semibold leading-7 text-black">Research and
                        Discovery</dt><dd class="mt-2 text-base text-center sm:text-left leading-7 text-gray-500">Morbi
                        viverra dui mi arcu sed. Tellus semper adipiscing suspendisse semper morbi. Odio urna massa nunc
                        massa.</dd>
                    </div>
                </div>
                <div class="sm:flex-row items-center relative flex flex-col gap-6">
                    <div class="h-24 w-24 items-center justify-center rounded-full bg-white text-4xl font-bold flex border
                        border-gray-500">2</div>
                    <div class="w-full flex-1 flex-shrink-0">
                    <p class="mb-1.5 text-xs text-center sm:text-left text-black font-semibold">Feb 2024</p>
                    <dt class="text-xl text-center sm:text-left font-semibold leading-7 text-black">Product
                        Development</dt><dd class="mt-2 text-center sm:text-left text-base leading-7 text-gray-500">Morbi
                        viverra dui mi arcu sed. Tellus semper adipiscing suspendisse semper morbi. Odio urna massa nunc
                        massa.</dd>
                    </div>
                </div>
                <div class="sm:flex-row items-center relative flex flex-col gap-6">
                    <div class="h-24 w-24 items-center justify-center rounded-full bg-white text-4xl font-bold flex border
                        border-gray-500">3</div>
                    <div class="w-full flex-1 flex-shrink-0">
                    <p class="mb-1.5 text-center sm:text-left text-xs text-black font-semibold">Apr 2024</p>
                    <dt class="text-xl text-center sm:text-left font-semibold leading-7 text-black">Product Testing</dt><dd class="mt-2 text-center sm:text-left text-base leading-7 text-gray-500">Morbi viverra dui mi arcu sed.
                        Tellus semper adipiscing suspendisse semper morbi. Odio urna massa nunc massa.</dd>
                    </div>
                </div>
                <div class="sm:flex-row items-center relative flex flex-col gap-6">
                    <div class="h-24 w-24 items-center justify-center rounded-full bg-white text-4xl font-bold flex border
                        border-gray-500">4</div>
                    <div class="w-full flex-1 flex-shrink-0">
                    <p class="mb-1.5 text-xs text-center sm:text-left text-black font-semibold">May 2024</p>
                    <dt class="text-xl text-center sm:text-left font-semibold leading-7 text-black">Product Launch</dt><dd class="mt-2 text-center sm:text-left text-base leading-7 text-gray-500">Morbi viverra dui mi arcu sed.
                        Tellus semper adipiscing suspendisse semper morbi. Odio urna massa nunc massa.</dd>
                    </div>
                </div>
                </dl>
            </div>
            </div>
        </section>
        <section class="mx-auto py-20 max-w-screen-2xl p-4">
            <div class="mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
            <div class="mb-16">
                <p class="text-center text-gray-600 mb-2 font-medium tracking-wide uppercase">testimonial</p>
                <p class="text-4xl md:text-6xl font-bold tracking-tight text-center">What our happy user says!</p>
            </div>
            <div class="md:grid-cols-2 md:gap-8 md:max-w-2xl lg:max-w-full mx-auto grid grid-cols-1 gap-5 max-w-lg">
                <div class="bg-white rounded-xl w-full mx-auto group border border-solid border-gray-300 p-6 transition-all
                    duration-500 hover:border-black hover:shadow-sm">
                <div>
                    <p class="text-base text-black leading-6 pb-8 transition-all duration-500 group-hover:text-gray-800">With
                        their reliable and developer-friendly tools, our engineering team is freed up to focus on product and
                        customer experiences.</p>
                </div>
                <div class="items-center pt-5 flex gap-5">
                    <img alt="Kate image" src="https://devwares-pull-zone.b-cdn.net/mockimages/Sophie%20Moore%20-%20Circle%20Small.png" class="object-cover h-10 w-10 rounded-full">
                    <div class="block">
                    <p class="text-gray-900 font-medium mb-1 transition-all duration-500">Kate Henshawlee</p>
                    <span class="text-sm leading-4 text-gray-500">Head of Products, Xamplified</span>
                    </div>
                </div>
                </div>
                <div class="bg-white rounded-xl w-full mx-auto group border border-solid border-gray-300 p-6 transition-all
                    duration-500 hover:border-black hover:shadow-sm">
                <div>
                    <p class="text-base text-black leading-6 pb-8 transition-all duration-500
                        group-hover:text-gray-800">Awesome! Just got a pro subscription and I am quite surprised to find that
                        this is actually quite advanced and super cool! such a time saver</p>
                </div>
                <div class="items-center pt-5 flex gap-5">
                    <img alt="Dave image" src="https://images.pexels.com/photos/1680172/pexels-photo-1680172.jpeg?auto=compress&amp;cs=tinysrgb&amp;dpr=2&amp;w=500" class="object-cover h-10 w-10 rounded-full">
                    <div class="block">
                    <p class="text-gray-900 font-medium mb-1 transition-all duration-500">Dave Stevens</p>
                    <span class="text-sm leading-4 text-gray-500">CTO/Co-founder, Deleadr</span>
                    </div>
                </div>
                </div>
            </div>
            </div>
        </section>
        <section class="bg-white py-20">
            <div class="mx-auto p-4 max-w-screen-2xl relative isolate">
            <svg class="w-[468px] h-[788px] absolute top-0 right-0 -z-10 transform-gpu overflow-hidden blur-2xl" viewBox="0 0
                468 788" fill="none" xmlns="http://www.w3.org/2000/svg" id="Windframe_f01Ef8942OV"><circle cx="44.5105" cy="378.637" r="156.383" fill="#4A3AFF"></circle><circle cx="119.803" cy="529.24" r="156.383" fill="#702DFF"></circle><circle cx="173.364" cy="372.857" r="156.383" fill="#2D5BFF"></circle><g filter="url(#filter0_b_1410_520)"><circle cx="73.5409" cy="394.049" r="393.819" fill="white" fill-opacity="0.79"></circle></g><defs><filter x="-460.404" y="-139.896" width="1067.89" height="1067.89" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood><feGaussianBlur in="BackgroundImageFix" stdDeviation="70.063"></feGaussianBlur><feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1410_520"></feComposite><feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1410_520" result="shape"></feBlend></filter></defs></svg>
            <div class="mx-auto sm:text-center max-w-4xl">
                <p class="text-center text-gray-500 font-medium tracking-wide uppercase">pricing</p>
                <p class="mt-2 text-4xl lg:text-6xl sm:text-5xl font-bold tracking-tight text-gray-900 text-center">Affordable
                    plans for you and your team</p>
            </div>
            <p class="mx-auto mt-6 text-lg leading-8 text-gray-600 sm:text-center max-w-2xl">Distinctio et nulla eum soluta et
                neque labore quibusdam. Saepe et quasi iusto modi velit ut non voluptas in. Explicabo id ut laborum.</p>
            <div class="mx-auto mt-20 lg:max-w-4xl lg:grid-cols-2 grid max-w-md grid-cols-1 gap-8">
                <div class="justify-between rounded-3xl bg-white shadow-xl sm:p-10 flex flex-col p-8 ring-1 ring-gray-900/10">
                <div>
                    <p class="text-base font-semibold leading-7 text-black" id="tier-hobby">Basic</p>
                    <div class="mt-4 items-baseline flex gap-x-2">
                    <span class="text-5xl font-bold tracking-tight text-gray-900">$25</span>
                    <span class="text-base font-semibold leading-7 text-gray-600">/month</span>
                    </div>
                    <p class="mt-6 text-base leading-7 text-gray-600">Modi dolorem expedita deleniti. Corporis iste qui
                        inventore pariatur adipisci vitae.</p>
                    <ul role="list" class="mt-10 text-sm leading-6 text-gray-600 space-y-4">
                    <li class="flex gap-x-3">
                        <svg class="h-6 w-5 text-black flex-none" viewBox="0 0 20 20" fill="currentColor" aria-hidden="" id="Windframe_68ImVHPNken"><path fillrule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8
                            10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0
                            011.05-.143z" clip-rule="evenodd"></path></svg>
                        5 products
                    </li>
                    <li class="flex gap-x-3">
                        <svg class="h-6 w-5 text-black flex-none" viewBox="0 0 20 20" fill="currentColor" aria-hidden="" id="Windframe_1QV8F1QLvZr"><path fillrule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8
                            10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0
                            011.05-.143z" clip-rule="evenodd"></path></svg>
                        Up to 10GB
                    </li>
                    <li class="flex gap-x-3">
                        <svg class="h-6 w-5 text-black flex-none" viewBox="0 0 20 20" fill="currentColor" aria-hidden="" id="Windframe_LJ_CVSr6Id4"><path fillrule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8
                            10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0
                            011.05-.143z" clip-rule="evenodd"></path></svg>
                        Basic analytics
                    </li>
                    <li class="flex gap-x-3">
                        <svg class="h-6 w-5 text-black flex-none" viewBox="0 0 20 20" fill="currentColor" aria-hidden="" id="Windframe_1cIYa6NyIk4"><path fillrule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8
                            10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0
                            011.05-.143z" clip-rule="evenodd"></path></svg>
                        48-hour support response time
                    </li>
                    </ul>
                </div>
                <a href="#" aria-describedby="tier-hobby" class="mt-8 rounded-md bg-black px-3.5 py-2 text-center text-sm
                    font-semibold leading-6 text-white shadow-sm block hover:bg-black focus-visible:outline
                    focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-black">Get started today</a>
                </div>
                <div class="justify-between rounded-3xl bg-white shadow-xl sm:p-10 flex flex-col p-8 ring-1 ring-gray-900/10">
                <div>
                    <p class="text-base font-semibold leading-7 text-black" id="tier-team">Team</p>
                    <div class="mt-4 items-baseline flex gap-x-2">
                    <span class="text-5xl font-bold tracking-tight text-gray-900">$75</span>
                    <span class="text-base font-semibold leading-7 text-gray-600">/month</span>
                    </div>
                    <p class="mt-6 text-base leading-7 text-gray-600">Explicabo quo fugit vel facere ullam corrupti non dolores.
                        Expedita eius sit sequi.</p>
                    <ul role="list" class="mt-10 text-sm leading-6 text-gray-600 space-y-4">
                    <li class="flex gap-x-3">
                        <svg class="h-6 w-5 text-black flex-none" viewBox="0 0 20 20" fill="currentColor" aria-hidden="" id="Windframe_1wRBLOTObtx"><path fillrule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8
                            10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0
                            011.05-.143z" clip-rule="evenodd"></path></svg>
                        25 products
                    </li>
                    <li class="flex gap-x-3">
                        <svg class="h-6 w-5 text-black flex-none" viewBox="0 0 20 20" fill="currentColor" aria-hidden="" id="Windframe_UeGfie54SPL"><path fillrule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8
                            10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0
                            011.05-.143z" clip-rule="evenodd"></path></svg>
                        200 GB
                    </li>
                    <li class="flex gap-x-3">
                        <svg class="h-6 w-5 text-black flex-none" viewBox="0 0 20 20" fill="currentColor" aria-hidden="" id="Windframe_NULtjalCX41"><path fillrule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8
                            10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0
                            011.05-.143z" clip-rule="evenodd"></path></svg>
                        Advanced analytics
                    </li>
                    <li class="flex gap-x-3">
                        <svg class="h-6 w-5 text-black flex-none" viewBox="0 0 20 20" fill="currentColor" aria-hidden="" id="Windframe_3uHsUJIqyMK"><path fillrule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8
                            10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0
                            011.05-.143z" clip-rule="evenodd"></path></svg>
                        1-hour, dedicated support response time
                    </li>
                    <li class="flex gap-x-3">
                        <svg class="h-6 w-5 text-black flex-none" viewBox="0 0 20 20" fill="currentColor" aria-hidden="" id="Windframe_EVqJnZpsY82"><path fillrule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8
                            10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0
                            011.05-.143z" clip-rule="evenodd"></path></svg>
                        Marketing automations
                    </li>
                    </ul>
                </div>
                <a href="#" aria-describedby="tier-team" class="mt-8 rounded-md bg-black px-3.5 py-2 text-center text-sm
                    font-semibold leading-6 text-white shadow-sm block hover:bg-black focus-visible:outline
                    focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-black">Get started today</a>
                </div>
            </div>
            </div>
        </section>
        <section class="bg-white py-20">
            <div class="mx-auto px-4 text-center max-w-screen-2xl relative isolate">
            <svg class="h-[788px] w-[300px] absolute left-0 -z-10 overflow-hidden blur-2xl left-20 -top-60" viewBox="0 0 468
                788" fill="none" xmlns="http://www.w3.org/2000/svg" id="Windframe_msEG1FPOQGT"><circle cx="44.5105" cy="378.637" r="156.383" fill="#4A3AFF"></circle><circle cx="119.803" cy="529.24" r="156.383" fill="#702DFF"></circle><circle cx="173.364" cy="372.857" r="156.383" fill="#2D5BFF"></circle><g filter="url(#filter0_b_1410_520)"><circle cx="73.5409" cy="394.049" r="393.819" fill="white" fill-opacity="0.79"></circle></g><defs><filter x="-460.404" y="-139.896" width="1067.89" height="1067.89" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood><feGaussianBlur in="BackgroundImageFix" stdDeviation="70.063"></feGaussianBlur><feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1410_520"></feComposite><feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1410_520" result="shape"></feBlend></filter></defs></svg>
            <p class="mb-2 tracking-widest text-sm font-bold text-gray-600 text-center uppercase">team</p>
            <div class="mx-auto max-w-2xl">
                <p class="text-4xl font-bold tracking-tight text-black sm:text-6xl">Meet our team</p>
                <p class="mt-4 text-lg leading-8 text-gray-600">We’re a dynamic group of individuals who are passionate about
                    what we do and dedicated to delivering the best results for our clients.</p>
            </div>
            <ul role="list" class="mx-auto mt-12 sm:grid-cols-2 lg:mx-0 lg:max-w-none lg:grid-cols-3 lg:gap-16 grid max-w-2xl
                grid-cols-1 gap-10">
                <li>
                <img alt="" src="https://devwares-pull-zone.b-cdn.net/mockimages/John%20Carter%20-%20Cirlce%20Small.png" class="max-h-[400px] object-cover object-top mx-auto w-full rounded-lg">
                <div class="px-8 py-6">
                    <p class="text-2xl font-semibold leading-7 tracking-tight text-black">Reeyha Gerolds</p>
                    <p class="text-lg leading-6 text-black pt-1.5">Marketing Executive</p>
                    <ul role="list" class="mt-4 justify-center flex gap-x-4">
                    <li>
                        <a href="#" class="text-black hover:text-gray-700">
                        <span class="sr-only">X</span>
                        <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 20 20" aria-hidden="" id="Windframe_ERO8Zx6dn7m"><path d="M11.4678 8.77491L17.2961 2H15.915L10.8543 7.88256L6.81232
                            2H2.15039L8.26263 10.8955L2.15039 18H3.53159L8.87581 11.7878L13.1444 18H17.8063L11.4675
                            8.77491H11.4678ZM9.57608 10.9738L8.95678 10.0881L4.02925 3.03974H6.15068L10.1273 8.72795L10.7466
                            9.61374L15.9156 17.0075H13.7942L9.57608 10.9742V10.9738Z"></path></svg>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="text-black hover:text-gray-700">
                        <span class="sr-only">LinkedIn</span>
                        <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 20 20" aria-hidden="" id="Windframe_wzE0bP827dJ"><path fillrule="evenodd" d="M16.338
                            16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601
                            2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778
                            3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337
                            9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328
                            19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z" clip-rule="evenodd"></path></svg>
                        </a>
                    </li>
                    </ul>
                </div>
                </li>
                <li>
                <img alt="" src="https://devwares-pull-zone.b-cdn.net/mockimages/Sophie%20Moore%20-%20Circle%20Small.png" class="max-h-[400px] object-cover object-top mx-auto w-full rounded-lg">
                <div class="px-8 py-6">
                    <p class="text-2xl font-semibold leading-7 tracking-tight text-black">Martha Crawsford</p>
                    <p class="text-lg leading-6 text-black pt-1.5">AI Engineer</p>
                    <ul role="list" class="mt-4 justify-center flex gap-x-4">
                    <li>
                        <a href="#" class="text-black hover:text-gray-700">
                        <span class="sr-only">X</span>
                        <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 20 20" aria-hidden="" id="Windframe_rRq5doD6T3ww"><path d="M11.4678 8.77491L17.2961 2H15.915L10.8543 7.88256L6.81232
                            2H2.15039L8.26263 10.8955L2.15039 18H3.53159L8.87581 11.7878L13.1444 18H17.8063L11.4675
                            8.77491H11.4678ZM9.57608 10.9738L8.95678 10.0881L4.02925 3.03974H6.15068L10.1273 8.72795L10.7466
                            9.61374L15.9156 17.0075H13.7942L9.57608 10.9742V10.9738Z"></path></svg>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="text-black hover:text-gray-700">
                        <span class="sr-only">LinkedIn</span>
                        <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 20 20" aria-hidden="" id="Windframe_lDVuIr_VXEEx"><path fillrule="evenodd" d="M16.338
                            16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601
                            2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778
                            3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337
                            9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328
                            19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z" clip-rule="evenodd"></path></svg>
                        </a>
                    </li>
                    </ul>
                </div>
                </li>
                <li>
                <img alt="" src="https://devwares-pull-zone.b-cdn.net/mockimages/Mike%20Warren%20-%20Circle%20Small.png" class="max-h-[400px] object-cover object-top mx-auto w-full rounded-lg">
                <div class="px-8 py-6">
                    <p class="text-2xl font-semibold leading-7 tracking-tight text-black">Michael Angelo</p>
                    <p class="text-lg leading-6 text-black pt-1.5">Software Engineer</p>
                    <ul role="list" class="mt-4 justify-center flex gap-x-4">
                    <li>
                        <a href="#" class="text-black hover:text-gray-700">
                        <span class="sr-only">X</span>
                        <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 20 20" aria-hidden="" id="Windframe_km8h3JJmpPuq"><path d="M11.4678 8.77491L17.2961 2H15.915L10.8543 7.88256L6.81232
                            2H2.15039L8.26263 10.8955L2.15039 18H3.53159L8.87581 11.7878L13.1444 18H17.8063L11.4675
                            8.77491H11.4678ZM9.57608 10.9738L8.95678 10.0881L4.02925 3.03974H6.15068L10.1273 8.72795L10.7466
                            9.61374L15.9156 17.0075H13.7942L9.57608 10.9742V10.9738Z"></path></svg>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="text-black hover:text-gray-700">
                        <span class="sr-only">LinkedIn</span>
                        <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 20 20" aria-hidden="" id="Windframe_tTBxBmJICKcL"><path fillrule="evenodd" d="M16.338
                            16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601
                            2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778
                            3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337
                            9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328
                            19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z" clip-rule="evenodd"></path></svg>
                        </a>
                    </li>
                    </ul>
                </div>
                </li>
            </ul>
            </div>
        </section>
        <section class="bg-white mx-auto px-4 max-w-screen-2xl">
            <p class="mb-2 tracking-widest text-sm font-bold text-gray-600 mx-auto uppercase max-w-4xl">FAQ</p>
            <div class="mx-auto max-w-4xl divide-y divide-gray-900/10">
            <p class="text-4xl font-bold leading-10 tracking-tight text-gray-900 lg:text-6xl justify-items-center">Frequently
                asked questions</p>
            <dl class="mt-10 space-y-6 divide-y divide-gray-900/10">
                <div class="pt-6">
                <dt class="text-left text-base font-semibold leading-7 text-gray-900">What's the best thing about
                    Switzerland?</dt>
                <dd class="mt-2 pr-12">
                    <p class="text-base leading-7 text-gray-600">I don't know, but the flag is a big plus. Lorem ipsum dolor sit
                        amet consectetur adipisicing elit. Quas cupiditate laboriosam fugiat.</p>
                </dd>
                </div>
                <div class="pt-6">
                <dt class="text-left text-base font-semibold leading-7 text-gray-900">What's the best thing about
                    Switzerland?</dt>
                <dd class="mt-2 pr-12">
                    <p class="text-base leading-7 text-gray-600">I don't know, but the flag is a big plus. Lorem ipsum dolor sit
                        amet consectetur adipisicing elit. Quas cupiditate laboriosam fugiat.</p>
                </dd>
                </div>
                <div class="pt-6">
                <dt class="text-left text-base font-semibold leading-7 text-gray-900">What's the best thing about
                    Switzerland?</dt>
                <dd class="mt-2 pr-12">
                    <p class="text-base leading-7 text-gray-600">I don't know, but the flag is a big plus. Lorem ipsum dolor sit
                        amet consectetur adipisicing elit. Quas cupiditate laboriosam fugiat.</p>
                </dd>
                </div>
                <div class="pt-6">
                <dt class="text-left text-base font-semibold leading-7 text-gray-900">What's the best thing about
                    Switzerland?</dt>
                <dd class="mt-2 pr-12">
                    <p class="text-base leading-7 text-gray-600">I don't know, but the flag is a big plus. Lorem ipsum dolor sit
                        amet consectetur adipisicing elit. Quas cupiditate laboriosam fugiat.</p>
                </dd>
                </div>
                <div class="pt-6">
                <dt class="text-left text-base font-semibold leading-7 text-gray-900">What's the best thing about
                    Switzerland?</dt>
                <dd class="mt-2 pr-12">
                    <p class="text-base leading-7 text-gray-600">I don't know, but the flag is a big plus. Lorem ipsum dolor sit
                        amet consectetur adipisicing elit. Quas cupiditate laboriosam fugiat.</p>
                </dd>
                </div>
                <div class="pt-6">
                <dt class="text-left text-base font-semibold leading-7 text-gray-900">What's the best thing about
                    Switzerland?</dt>
                <dd class="mt-2 pr-12">
                    <p class="text-base leading-7 text-gray-600">I don't know, but the flag is a big plus. Lorem ipsum dolor sit
                        amet consectetur adipisicing elit. Quas cupiditate laboriosam fugiat.</p>
                </dd>
                </div>
            </dl>
            </div>
        </section>
        <section class="py-20 mx-auto px-4 max-w-screen-2xl">
            <div class="lg:grid-cols-2 grid grid-cols-1">
            <div class="mb-10 lg:mb-0">
                <div class="h-full w-full group">
                <div class="h-full relative">
                    <img alt="ContactUs tailwind section" src="https://images.unsplash.com/photo-1600275669439-14e40452d20b?q=80&amp;w=2667&amp;auto=format&amp;fit=crop&amp;ixlib=rb-4.0.3&amp;ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" class="grayscale h-full w-full rounded-2xl lg:rounded-l-2xl lg:rounded-r-none">
                    <p class="text-4xl font-bold leading-10 text-white absolute left-11 top-11">Contact us</p>
                    <div class="w-full lg:p-11 absolute bottom-0 p-5">
                    <div class="rounded-lg bg-white block p-6">
                        <a href="#" class="mb-6 items-center flex">
                        <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg" class="" id="Windframe__LHuMiXYqauB"><path d="M22.3092 18.3098C22.0157 18.198 21.8689 18.1421 21.7145
                            18.1287C21.56 18.1154 21.4058 18.1453 21.0975 18.205L17.8126 18.8416C17.4392 18.9139 17.2525
                            18.9501 17.0616 18.9206C16.8707 18.891 16.7141 18.8058 16.4008 18.6353C13.8644 17.2551 12.1853
                            15.6617 11.1192 13.3695C10.9964 13.1055 10.935 12.9735 10.9133 12.8017C10.8917 12.6298 10.9218
                            12.4684 10.982 12.1456L11.6196 8.72559C11.6759 8.42342 11.7041 8.27233 11.6908 8.12115C11.6775
                            7.96998 11.6234 7.82612 11.5153 7.5384L10.6314 5.18758C10.37 4.49217 10.2392 4.14447 9.95437
                            3.94723C9.6695 3.75 9.29804 3.75 8.5551 3.75H5.85778C4.58478 3.75 3.58264 4.8018 3.77336
                            6.06012C4.24735 9.20085 5.64674 14.8966 9.73544 18.9853C14.0295 23.2794 20.2151 25.1426 23.6187
                            25.884C24.9335 26.1696 26.0993 25.1448 26.0993 23.7985V21.2824C26.0993 20.5428 26.0993 20.173
                            25.9034 19.8888C25.7076 19.6046 25.362 19.4729 24.6708 19.2096L22.3092 18.3098Z" stroke="currentColor" strokewidth="1" strokelinecap="round" strokelinejoin="round"></path></svg>
                        <p class="ml-5 text-base font-normal leading-6 text-black">123-456-7890</p>
                        </a>
                        <a href="#" class="mb-6 items-center flex">
                        <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg" class="" id="Windframe_ss8piW59VqxE"><path d="M2.81501 8.75L10.1985 13.6191C12.8358 15.2015 14.1544
                            15.9927 15.6032 15.9582C17.0519 15.9237 18.3315 15.0707 20.8905 13.3647L27.185 8.75M12.5
                            25H17.5C22.214 25 24.5711 25 26.0355 23.5355C27.5 22.0711 27.5 19.714 27.5 15C27.5 10.286 27.5
                            7.92893 26.0355 6.46447C24.5711 5 22.214 5 17.5 5H12.5C7.78595 5 5.42893 5 3.96447 6.46447C2.5
                            7.92893 2.5 10.286 2.5 15C2.5 19.714 2.5 22.0711 3.96447 23.5355C5.42893 25 7.78595 25 12.5 25Z" stroke="currentColor" strokewidth="1" strokelinecap="round"></path></svg>
                        <p class="ml-5 text-base font-normal leading-6 text-black"><EMAIL></p>
                        </a>
                        <a href="#" class="items-center flex">
                        <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg" class="" id="Windframe_IPc5ltkAAmXc"><path d="M25 12.9169C25 17.716 21.1939 21.5832 18.2779
                            24.9828C16.8385 26.6609 16.1188 27.5 15 27.5C13.8812 27.5 13.1615 26.6609 11.7221 24.9828C8.80612
                            21.5832 5 17.716 5 12.9169C5 10.1542 6.05357 7.5046 7.92893 5.55105C9.8043 3.59749 12.3478 2.5 15
                            2.5C17.6522 2.5 20.1957 3.59749 22.0711 5.55105C23.9464 7.5046 25 10.1542 25 12.9169Z" stroke="currentColor" strokewidth="1"></path><path d="M17.5 11.6148C17.5 13.0531 16.3807 14.219 15
                            14.219C13.6193 14.219 12.5 13.0531 12.5 11.6148C12.5 10.1765 13.6193 9.01058 15 9.01058C16.3807
                            9.01058 17.5 10.1765 17.5 11.6148Z" stroke="currentColor" strokewidth="1"></path></svg>
                        <p class="ml-5 text-base font-normal leading-6 text-black">Silicon Valley, San Francisco Bay,
                            California, U.S.</p>
                        </a>
                    </div>
                    </div>
                </div>
                </div>
            </div>
            <div class="rounded-2xl bg-white lg:rounded-r-2xl lg:p-11 p-5">
                <p class="mb-11 text-4xl font-semibold leading-10 text-black">Send Us A Message</p>
                <input type="text" placeholder="Name" class="border border-gray-300 placeholder-gray-400 focus:outline-none
                    mb-10 h-12 w-full rounded-lg bg-transparent pl-4 text-lg font-normal leading-7 text-black shadow-sm">
                <input type="text" placeholder="Email" class="border border-gray-300 placeholder-gray-400 focus:outline-none
                    mb-10 h-12 w-full rounded-lg bg-transparent pl-4 text-lg font-normal leading-7 text-black shadow-sm">
                <input type="text" placeholder="Phone" class="border border-gray-300 placeholder-gray-400 focus:outline-none
                    mb-10 h-12 w-full rounded-lg bg-transparent pl-4 text-lg font-normal leading-7 text-black shadow-sm">
                <div class="mb-10">
                <p class="mb-4 text-lg font-normal leading-7 text-black">Preferred method of communication</p>
                <div class="flex">
                    <div class="mr-11 items-center flex gap-2">
                    <input type="radio" name="radio-group" class="checked:border-black checked:bg-black checked:bg-center
                        checked:bg-no-repeat" id="radio-group-1">
                    <label for="radio-group-1" class="items-center text-base font-normal leading-6 text-black flex
                        cursor-pointer">Email</label>
                    </div>
                    <div class="items-center flex gap-2">
                    <input type="radio" name="radio-group" class="checked:border-black checked:bg-black checked:bg-center
                        checked:bg-no-repeat" id="radio-group-2">
                    <label for="radio-group-2" class="items-center text-base font-normal leading-6 text-black flex
                        cursor-pointer">Phone</label>
                    </div>
                </div>
                </div>
                <input type="text" placeholder="Message" class="border border-gray-300 placeholder-gray-400 focus:outline-none
                    mb-10 h-12 w-full rounded-lg bg-transparent pl-4 text-lg font-normal leading-7 text-black shadow-sm">
                <button type="submit" style="font-family: Arial" class="transition-all duration-700 hover:bg-black h-12 w-full
                    rounded-lg bg-black text-base font-semibold leading-6 text-white shadow-sm">Send</button>
            </div>
            </div>
        </section>
        <section class="bg-white mx-auto py-8 px-6 max-w-screen-2xl overflow-hidden">
            <div class="bg-white text-black px-6 py-20 text-center shadow-2xl sm:rounded-3xl sm:px-16 relative isolate
                overflow-hidden">
            <svg class="w-[600px] h-[400px] absolute -z-10 overflow-hidden blur-2xl right-88 right-90 right-92 right-93 right-100 right-80" viewBox="0 0 468 788" fill="none" xmlns="http://www.w3.org/2000/svg" id="Windframe_WO2n3ZxBwuhz"><circle cx="44.5105" cy="378.637" r="156.383" fill="#4A3AFF"></circle><circle cx="119.803" cy="529.24" r="156.383" fill="#702DFF"></circle><circle cx="173.364" cy="372.857" r="156.383" fill="#2D5BFF"></circle><g filter="url(#filter0_b_1410_520)"><circle cx="73.5409" cy="394.049" r="393.819" fill="white" fill-opacity="0.79"></circle></g><defs><filter x="-460.404" y="-139.896" width="1067.89" height="1067.89" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood><feGaussianBlur in="BackgroundImageFix" stdDeviation="70.063"></feGaussianBlur><feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1410_520"></feComposite><feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1410_520" result="shape"></feBlend></filter></defs></svg>
            <p class="mx-auto text-4xl font-bold tracking-tight md:text-6xl max-w-4xl">The best way to organize and improve
                your work</p>
            <p class="mx-auto mt-6 text-lg leading-8 text-gray-600 max-w-xl">Lorem ipsum dolor sit, amet consectetur
                adipisicing elit neque culpa consectetur adipisicing elit neque culpa consectetur.</p>
            <div class="mt-10 items-center justify-center flex gap-x-6">
                <a href="#" class="rounded-md bg-black text-white px-3.5 py-2.5 text-sm font-semibold shadow-sm
                    hover:bg-gray-100 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2
                    focus-visible:outline-white">Get started</a>
            </div>
            </div>
        </section>
        <footer aria-labelledby="footer-heading" class="bg-white">
            <p class="sr-only" id="footer-heading">Footer</p>
            <div class="mx-auto px-4 py-20 max-w-screen-2xl">
            <div class="xl:grid xl:grid-cols-3 xl:gap-8">
                <div class="mt-10 xl:mt-0">
                <p class="text-sm font-semibold leading-6 text-black">Subscribe to our newsletter</p>
                <p class="mt-2 text-sm leading-6 text-gray-800">The latest news, articles, and resources, sent to your inbox
                    weekly.</p>
                <form class="mt-6 sm:flex sm:max-w-md">
                    <label for="email-address" class="sr-only">Email address</label>
                    <input type="email" placeholder="Enter your email" name="email-address" autocomplete="email" required="" class="min-w-0 appearance-none border-0 ring-1 ring-inset ring-white/10 placeholder:text-gray-500
                        focus:ring-2 focus:ring-inset focus:ring-black focus:outline-none w-full rounded-md bg-gray-50 px-3
                        py-1.5 text-base text-black shadow-sm sm:w-64 sm:text-sm sm:leading-6 xl:w-full" id="email-address">
                    <div class="mt-4 sm:ml-4 sm:mt-0 sm:flex-shrink-0">
                    <button type="submit" style="font-family: Arial" class="flex hover:bg-black focus-visible:outline
                        focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-black w-full items-center
                        justify-center rounded-md bg-black px-3 py-2 text-sm font-semibold text-white
                        shadow-sm">Subscribe</button>
                    </div>
                </form>
                </div>
                <div class="mt-10 xl:mt-0 xl:col-span-2 grid grid-cols-2 gap-8">
                <div class="md:grid md:grid-cols-2 md:gap-8">
                    <div>
                    <p class="text-sm font-semibold leading-6 text-black">Category</p>
                    <ul role="list" class="mt-6 space-y-4">
                        <li>
                        <a href="#" class="text-sm leading-6 text-gray-800 hover:text-black">Marketing</a>
                        </li>
                        <li>
                        <a href="#" class="text-sm leading-6 text-gray-800 hover:text-black">Analytics</a>
                        </li>
                        <li>
                        <a href="#" class="text-sm leading-6 text-gray-800 hover:text-black">Commerce</a>
                        </li>
                    </ul>
                    </div>
                    <div class="mt-10 md:mt-0">
                    <p class="text-sm font-semibold leading-6 text-black">Category</p>
                    <ul role="list" class="mt-6 space-y-4">
                        <li>
                        <a href="#" class="text-sm leading-6 text-gray-800 hover:text-black">Pricing</a>
                        </li>
                        <li>
                        <a href="#" class="text-sm leading-6 text-gray-800 hover:text-black">Guides</a>
                        </li>
                        <li>
                        <a href="#" class="text-sm leading-6 text-gray-800 hover:text-black">Mission and Values</a>
                        </li>
                    </ul>
                    </div>
                </div>
                <div class="md:grid md:grid-cols-2 md:gap-8">
                    <div>
                    <p class="text-sm font-semibold leading-6 text-black">Category</p>
                    <ul role="list" class="mt-6 space-y-4">
                        <li>
                        <a href="#" class="text-sm leading-6 text-gray-800 hover:text-black">About</a>
                        </li>
                        <li>
                        <a href="#" class="text-sm leading-6 text-gray-800 hover:text-black">Blog</a>
                        </li>
                        <li>
                        <a href="#" class="text-sm leading-6 text-gray-800 hover:text-black">Team</a>
                        </li>
                    </ul>
                    </div>
                    <div class="mt-10 md:mt-0">
                    <p class="text-sm font-semibold leading-6 text-black">Category</p>
                    <ul role="list" class="mt-6 space-y-4">
                        <li>
                        <a href="#" class="text-sm leading-6 text-gray-800 hover:text-black">Claim</a>
                        </li>
                        <li>
                        <a href="#" class="text-sm leading-6 text-gray-800 hover:text-black">Privacy</a>
                        </li>
                        <li>
                        <a href="#" class="text-sm leading-6 text-gray-800 hover:text-black">Terms</a>
                        </li>
                    </ul>
                    </div>
                </div>
                </div>
            </div>
            <div class="mt-16 pt-8 sm:mt-20 md:flex md:items-center md:justify-between lg:mt-24 border-t border-white/10">
                <div class="md:order-2 flex space-x-6">
                <a href="#" class="text-black hover:text-gray-700">
                    <span class="sr-only">Facebook</span>
                    <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="" id="Windframe_mojE30kCaFvF"><path fillrule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438
                        9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195
                        2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22
                        16.991 22 12z" clip-rule="evenodd"></path></svg>
                </a>
                <a href="#" class="text-black hover:text-gray-700">
                    <span class="sr-only">Instagram</span>
                    <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="" id="Windframe_dpGfnbQZsMAz"><path fillrule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0
                        011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0
                        2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0
                        01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643
                        0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0
                        01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clip-rule="evenodd"></path></svg>
                </a>
                <a href="#" class="text-black hover:text-gray-700">
                    <span class="sr-only">X</span>
                    <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="" id="Windframe_-lcqMgZzEHys"><path d="M13.6823 10.6218L20.2391 3H18.6854L12.9921 9.61788L8.44486 3H3.2002L10.0765 13.0074L3.2002
                        21H4.75404L10.7663 14.0113L15.5685 21H20.8131L13.6819 10.6218H13.6823ZM11.5541 13.0956L10.8574
                        12.0991L5.31391 4.16971H7.70053L12.1742 10.5689L12.8709 11.5655L18.6861 19.8835H16.2995L11.5541
                        13.096V13.0956Z"></path></svg>
                </a>
                <a href="#" class="text-black hover:text-gray-700">
                    <span class="sr-only">GitHub</span>
                    <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="" id="Windframe_sdU7kVUdaFkK"><path fillrule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839
                        9.504.5.092.682-.217.682-.483
                        0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clip-rule="evenodd"></path></svg>
                </a>
                <a href="#" class="text-black hover:text-gray-700">
                    <span class="sr-only">YouTube</span>
                    <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="" id="Windframe_fo4WCsBU3JFt"><path fillrule="evenodd" d="M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418
                        4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0
                        1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998
                        5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z" clip-rule="evenodd"></path></svg>
                </a>
                </div>
                <p class="mt-8 text-xs leading-5 text-gray-400 md:order-1 md:mt-0">© 2024 Windframe, Inc. All rights
                    reserved.</p>
            </div>
            </div>
        </footer>
        <script>
            const btn = document.querySelector(".mobile-menu-button");
            const menu = document.querySelector(".mobile-menu");
            btn.addEventListener("click", () => {
            menu.classList.toggle("hidden");
            });
        </script>
    </body>
</html>