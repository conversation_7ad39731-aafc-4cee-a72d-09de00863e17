<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GemAI - URL Shortener & Bio Pages</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #E0F2FE 0%, #FCE7F3 100%);
        }
        .hero-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        /* Animation classes */
        .fade-in {
            animation: fadeIn 0.6s ease-in-out;
        }
        .slide-up {
            animation: slideUp 0.5s ease-out;
        }
        .pulse {
            animation: pulse 2s infinite;
        }
        .float {
            animation: float 3s ease-in-out infinite;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes slideUp {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }
    </style>
    <script>
        if (localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.querySelector('html').setAttribute('data-theme', 'dark')
        } else {
            document.querySelector('html').setAttribute('data-theme', 'light')
        }
    </script>
</head>
<body class="dark:bg-gray-900">
    <!-- Hero Section with Integrated Navigation -->
    <section class="pt-8 py-20 px-4 sm:px-6 lg:px-8 relative dark:bg-gray-900">
        <div class="gradient-bg rounded-3xl py-8">
            <!-- Navigation -->
            <nav class="mx-5">
                <div class="bg-white dark:bg-gray-800 rounded-full shadow-xl max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between items-center h-20">
                        <div class="flex items-center">
                            <a href="#" class="text-2xl font-bold text-blue-600 dark:text-blue-400">GemAI</a>
                        </div>
                        <div class="hidden md:flex items-center space-x-8">
                            <a href="#" class="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">Product</a>
                            <a href="#" class="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">Solutions</a>
                            <a href="#" class="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">Pricing</a>
                            <a href="#" class="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">Resources</a>
                        </div>
                        <div class="hidden md:flex items-center space-x-4">
                            <!-- Dark Mode Toggle -->
                            <button id="darkModeToggle" class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                                <!-- Sun Icon (shown in dark mode) -->
                                <svg class="w-6 h-6 hidden dark:block text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
                                </svg>
                                <!-- Moon Icon (shown in light mode) -->
                                <svg class="w-6 h-6 block dark:hidden text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
                                </svg>
                            </button>
                            <a href="signin.html" class="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">Sign in</a>
                            <a href="signup.html" class="bg-blue-600 text-white dark:bg-blue-500 px-4 py-2 rounded-full hover:bg-blue-700 dark:hover:bg-blue-600">Sign up free</a>
                        </div>
                        <div class="md:hidden">
                            <button id="menubtn" class="text-gray-600 dark:text-gray-300">
                                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
                <!-- Mobile menu -->
                <div id="mobilebtn" class="hidden md:hidden bg-white dark:bg-gray-800 rounded-xl mt-2 shadow-lg p-4">
                    <div class="flex flex-col space-y-4">
                        <a href="#" class="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">Product</a>
                        <a href="#" class="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">Solutions</a>
                        <a href="#" class="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">Pricing</a>
                        <a href="#" class="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">Resources</a>
                        <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
                            <a href="signin.html" class="block text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 mb-2">Sign in</a>
                            <a href="signup.html" class="block bg-blue-600 text-white dark:bg-blue-500 px-4 py-2 rounded-full text-center hover:bg-blue-700 dark:hover:bg-blue-600">Sign up free</a>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Hero Content -->
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 hero-pattern">
                <div class="grid md:grid-cols-2 gap-12 items-center">
                    <div class="text-left fade-in">
                        <h1 class="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6 leading-tight">
                            Short links, big results
                        </h1>
                        <p class="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-2xl">
                            A URL shortener that includes detailed analytics, QR codes, and bio pages. 
                            Build your brand and track your links in one powerful platform.
                        </p>
                        <div class="flex flex-col sm:flex-row gap-4 mb-8">
                            <a href="#" class="bg-blue-600 dark:bg-blue-500 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-blue-700 dark:hover:bg-blue-600 text-center">
                                Get started for free
                            </a>
                            <a href="#" class="bg-white dark:bg-gray-800 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-50 dark:hover:bg-gray-700 text-center">
                                Learn more
                            </a>
                        </div>
                        <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                            <span>No credit card required</span>
                            <span class="mx-2">•</span>
                            <span>Free plan available</span>
                            <span class="mx-2">•</span>
                            <span>Cancel anytime</span>
                        </div>
                    </div>
                    <div class="relative float">
                        <!-- URL Shortener Demo -->
                        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6 max-w-md mx-auto">
                            <div class="flex items-center space-x-2 mb-4">
                                <div class="w-3 h-3 rounded-full bg-red-500"></div>
                                <div class="w-3 h-3 rounded-full bg-yellow-500"></div>
                                <div class="w-3 h-3 rounded-full bg-green-500"></div>
                                <div class="ml-auto text-sm text-gray-400">URL Shortener</div>
                            </div>
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
                                <div class="flex items-center mb-3">
                                    <span class="text-gray-400 mr-2">https://</span>
                                    <input type="text" placeholder="Paste long URL here" class="bg-transparent flex-1 outline-none text-gray-700 dark:text-gray-300">
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <span class="text-sm text-gray-500 dark:text-gray-400">gem.ai/</span>
                                        <input type="text" placeholder="custom-name" class="bg-transparent w-24 outline-none text-sm text-gray-700 dark:text-gray-300">
                                    </div>
                                    <button class="bg-blue-600 dark:bg-blue-500 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700 dark:hover:bg-blue-600">
                                        Shorten
                                    </button>
                                </div>
                            </div>
                            <!-- Recent Links -->
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div>
                                        <div class="text-blue-600 dark:text-blue-400 font-medium">gem.ai/product</div>
                                        <div class="text-xs text-gray-500 truncate max-w-[180px]">https://gemai.com/our-product-page...</div>
                                    </div>
                                    <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                        </svg>
                                    </button>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div>
                                        <div class="text-blue-600 dark:text-blue-400 font-medium">gem.ai/webinar</div>
                                        <div class="text-xs text-gray-500 truncate max-w-[180px]">https://gemai.com/webinar-registration...</div>
                                    </div>
                                    <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <!-- Notification Bubble -->
                            <div class="absolute -top-2 -right-2 w-6 h-6 bg-orange-400 rounded-full flex items-center justify-center pulse">
                                <span class="text-white text-xs">2</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Trusted By Section -->
    <section class="py-12 px-4 sm:px-6 lg:px-8 bg-white dark:bg-gray-900">
        <div class="max-w-7xl mx-auto">
            <p class="text-center text-gray-500 dark:text-gray-400 mb-8">Trusted by 20,000+ businesses worldwide</p>
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center justify-items-center">
                <img src="https://placehold.co/120x40/gray/white?text=Shopify" alt="Shopify" class="h-8 opacity-60 dark:invert">
                <img src="https://placehold.co/120x40/gray/white?text=Slack" alt="Slack" class="h-8 opacity-60 dark:invert">
                <img src="https://placehold.co/120x40/gray/white?text=Amazon" alt="Amazon" class="h-8 opacity-60 dark:invert">
                <img src="https://placehold.co/120x40/gray/white?text=Spotify" alt="Spotify" class="h-8 opacity-60 dark:invert">
                <img src="https://placehold.co/120x40/gray/white?text=Google" alt="Google" class="h-8 opacity-60 dark:invert">
                <img src="https://placehold.co/120x40/gray/white?text=Microsoft" alt="Microsoft" class="h-8 opacity-60 dark:invert">
            </div>
        </div>
    </section>

    <!-- Premium Features Section -->
    <section class="py-24 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
        <div class="max-w-7xl mx-auto">
            <!-- Header -->
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-8">
                    <span>Supercharge</span>
                    <span class="text-yellow-500 mx-2">⚡</span>
                    <span>your productivity</span>
                </h2>
                <button class="bg-blue-900 dark:bg-blue-700 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:bg-blue-800 dark:hover:bg-blue-600 transition-colors">
                    Get Started
                </button>
            </div>

            <!-- Feature Cards -->
            <div class="grid md:grid-cols-3 gap-8">
                <!-- URL Shortener Card -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                    <div class="bg-blue-50 dark:bg-blue-900/30 p-6">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">URL Shortener</h3>
                        <div class="bg-white dark:bg-gray-700 rounded-xl p-4 shadow-sm">
                            <div class="flex items-center space-x-2 mb-3">
                                <div class="w-3 h-3 rounded-full bg-red-500"></div>
                                <div class="w-3 h-3 rounded-full bg-yellow-500"></div>
                                <div class="w-3 h-3 rounded-full bg-green-500"></div>
                            </div>
                            <div class="bg-gray-50 dark:bg-gray-600 rounded-lg p-3 flex items-center">
                                <span class="text-gray-400 dark:text-gray-300 text-sm">gem.ai/your-link</span>
                                <span class="ml-auto text-blue-600 dark:text-blue-400">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                    </svg>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="flex flex-wrap gap-2 mb-6">
                            <span class="px-3 py-1 bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full text-sm">Analytics</span>
                            <span class="px-3 py-1 bg-purple-50 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 rounded-full text-sm">Custom Links</span>
                            <span class="px-3 py-1 bg-green-50 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded-full text-sm">UTM Builder</span>
                        </div>
                        <p class="text-gray-600 dark:text-gray-300 mb-6">Create branded short links with powerful analytics and customization options.</p>
                        <a href="#" class="text-blue-900 dark:text-blue-400 font-semibold hover:text-blue-800 dark:hover:text-blue-300 transition-colors">Learn more →</a>
                    </div>
                </div>

                <!-- Bio Pages Card -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                    <div class="bg-blue-50 dark:bg-blue-900/30 p-6">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Bio Pages</h3>
                        <div class="bg-white dark:bg-gray-700 rounded-xl p-4 shadow-sm">
                            <div class="flex flex-col items-center">
                                <div class="w-16 h-16 bg-gray-200 dark:bg-gray-600 rounded-full mb-3"></div>
                                <h4 class="font-medium text-gray-900 dark:text-white mb-2">@username</h4>
                                <div class="flex space-x-3 text-gray-600 dark:text-gray-400">
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M23 3a10.9 10.9 0 01-3.14 1.53 4.48 4.48 0 00-7.86 3v1A10.66 10.66 0 013 4s-4 9 5 13a11.64 11.64 0 01-7 2c9 5 20 0 20-11.5a4.5 4.5 0 00-.08-.83A7.72 7.72 0 0023 3z"/>
                                    </svg>
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 0C8.74 0 8.333.015 7.053.072 5.775.132 4.905.333 4.14.63c-.789.306-1.459.717-2.126 1.384S.935 3.35.63 4.14C.333 4.905.131 5.775.072 7.053.012 8.333 0 8.74 0 12s.015 3.667.072 4.947c.06 1.277.261 2.148.558 2.913.306.788.717 1.459 1.384 2.126.667.666 1.336 1.079 2.126 1.384.766.296 1.636.499 2.913.558C8.333 23.988 8.74 24 12 24s3.667-.015 4.947-.072c1.277-.06 2.148-.262 2.913-.558.788-.306 1.459-.718 2.126-1.384.666-.667 1.079-1.335 1.384-2.126.296-.765.499-1.636.558-2.913.06-1.28.072-1.687.072-4.947s-.015-3.667-.072-4.947c-.06-1.277-.262-2.149-.558-2.913-.306-.789-.718-1.459-1.384-2.126C21.319 1.347 20.651.935 19.86.63c-.765-.297-1.636-.499-2.913-.558C15.667.012 15.26 0 12 0zm0 2.16c3.203 0 3.585.016 4.85.071 1.17.055 1.805.249 2.227.415.562.217.96.477 1.382.896.419.42.679.819.896 1.381.164.422.36 1.057.413 2.227.057 1.266.07 1.646.07 4.85s-.015 3.585-.074 4.85c-.061 1.17-.256 1.805-.421 2.227-.224.562-.479.96-.897 1.382-.419.419-.824.679-1.38.896-.42.164-1.065.36-2.235.413-1.274.057-1.649.07-4.859.07-3.211 0-3.586-.015-4.859-.074-1.171-.061-1.816-.256-2.236-.421-.569-.224-.96-.479-1.379-.897-.421-.419-.69-.824-.9-1.38-.165-.42-.359-1.065-.42-2.235-.045-1.26-.061-1.649-.061-4.844 0-3.196.016-3.586.061-4.861.061-1.17.255-1.814.42-2.234.21-.57.479-.96.9-1.381.419-.419.81-.689 1.379-.898.42-.166 1.051-.361 2.221-.421 1.275-.045 1.65-.06 4.859-.06l.045.03zm0 3.678c-3.405 0-6.162 2.76-6.162 6.162 0 3.405 2.76 6.162 6.162 6.162 3.405 0 6.162-2.76 6.162-6.162 0-3.405-2.76-6.162-6.162-6.162zM12 16c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4zm7.846-10.405c0 .795-.646 1.44-1.44 1.44-.795 0-1.44-.646-1.44-1.44 0-.794.646-1.439 1.44-1.439.793-.001 1.44.645 1.44 1.439z"/>
                                    </svg>
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M22.675 0H1.325C.593 0 0 .593 0 1.325v21.351C0 23.407.593 24 1.325 24H12.82v-9.294H9.692v-3.622h3.128V8.413c0-3.1 1.893-4.788 4.659-4.788 1.325 0 2.463.099 2.795.143v3.24l-1.918.001c-1.504 0-1.795.715-1.795 1.763v2.313h3.587l-.467 3.622h-3.12V24h6.116c.73 0 1.323-.593 1.323-1.325V1.325C24 .593 23.407 0 22.675 0z"/>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="flex flex-wrap gap-2 mb-6">
                            <span class="px-3 py-1 bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full text-sm">Custom Domain</span>
                            <span class="px-3 py-1 bg-purple-50 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 rounded-full text-sm">Analytics</span>
                        </div>
                        <p class="text-gray-600 dark:text-gray-300 mb-6">Create beautiful, customizable bio pages that showcase your content and links.</p>
                        <a href="#" class="text-blue-900 dark:text-blue-400 font-semibold hover:text-blue-800 dark:hover:text-blue-300 transition-colors">Learn more →</a>
                    </div>
                </div>

                <!-- QR Codes Card -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                    <div class="bg-blue-50 dark:bg-blue-900/30 p-6">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">QR Codes</h3>
                        <div class="bg-white dark:bg-gray-700 rounded-xl p-4 shadow-sm flex justify-center">
                            <div class="w-32 h-32 bg-gray-900 dark:bg-gray-600 rounded-lg p-2">
                                <svg class="w-full h-full text-white" viewBox="0 0 100 100">
                                    <path fill="currentColor" d="M0 0h40v40H0zM60 0h40v40H60zM0 60h40v40H0z"/>
                                    <path fill="currentColor" d="M10 10h20v20H10zM70 10h20v20H70zM10 70h20v20H10z"/>
                                    <path fill="currentColor" d="M60 60h10v10H60zM80 60h20v20H80zM60 90h30v10H60z"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="flex flex-wrap gap-2 mb-6">
                            <span class="px-3 py-1 bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full text-sm">Custom Design</span>
                            <span class="px-3 py-1 bg-purple-50 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 rounded-full text-sm">Dynamic</span>
                            <span class="px-3 py-1 bg-green-50 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded-full text-sm">Analytics</span>
                        </div>
                        <p class="text-gray-600 dark:text-gray-300 mb-6">Generate branded QR codes with custom designs and tracking capabilities.</p>
                        <a href="#" class="text-blue-900 dark:text-blue-400 font-semibold hover:text-blue-800 dark:hover:text-blue-300 transition-colors">Learn more →</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Benefits Section -->
    <section class="py-20 px-4 sm:px-6 lg:px-8 bg-white dark:bg-gray-900">
        <div class="max-w-7xl mx-auto">
            <div class="flex justify-between items-center mb-16">
                <div>
                    <span class="text-sm text-gray-400 dark:text-gray-500 block mb-3 border border-gray-200 inline-block px-5 py-2 rounded-lg">Benefits</span>
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white max-w-2xl">
                        Why sales teams love our AI-Powered dashboard
                    </h2>
                </div>
                <button class="bg-blue-900 dark:bg-blue-700 text-white px-6 py-3 rounded-lg hover:bg-blue-800 dark:hover:bg-blue-600 transition-colors">
                    Learn more
                </button>
            </div>

            <div class="grid md:grid-cols-2 gap-12 items-center">
                <!-- Left side: Interface Mockup -->
                <div class="relative">
                    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6 max-w-md mx-auto">
                        <!-- Header with QR Code Design dropdown -->
                        <div class="flex items-center justify-between mb-8 border-b dark:border-gray-700 pb-4">
                            <div class="flex items-center space-x-2">
                                <span class="font-medium text-gray-800 dark:text-gray-200">QR Code Design</span>
                                <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                                </svg>
                            </div>
                        </div>

                        <!-- QR Code Display -->
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-8 mb-8 flex justify-center">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjIwMCIgaGVpZ2h0PSIyMDAiIGZpbGw9IndoaXRlIi8+PHBhdGggZD0iTTAgMGg3djdoLTd6TTkgMGg0djRoLTR6TTE0IDBoN3Y3aC03ek0wIDloNHY0aC00ek0xNCA5aDR2NGgtNHpNMCAxNGg3djdoLTd6TTkgMTRoNHY0aC00ek0xNCAxNGg3djdoLTd6IiBmaWxsPSJibGFjayIvPjwvc3ZnPg==" 
                                alt="QR Code" class="w-48 h-48">
                        </div>

                        <!-- URL Stats -->
                        <div class="grid grid-cols-2 gap-4 mb-8">
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm text-gray-500 dark:text-gray-400">sofume.com</span>
                                    <div class="flex items-center space-x-2">
                                        <button class="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"/>
                                            </svg>
                                        </button>
                                        <span class="text-sm font-medium text-gray-900 dark:text-gray-100">22.6k</span>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm text-gray-500 dark:text-gray-400">sofume.co</span>
                                    <div class="flex items-center space-x-2">
                                        <button class="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"/>
                                            </svg>
                                        </button>
                                        <span class="text-sm font-medium text-gray-900 dark:text-gray-100">16.2k</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Prompt Input -->
                        <div class="relative">
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">I want a minimalistic design where more branding...</p>
                                <button class="bg-purple-600 dark:bg-purple-500 text-white px-4 py-2 rounded-lg text-sm hover:bg-purple-700 dark:hover:bg-purple-600">
                                    Generate
                                </button>
                            </div>
                            <!-- Notification Bubble -->
                            <div class="absolute -top-2 -right-2 w-5 h-5 bg-orange-400 rounded-full flex items-center justify-center">
                                <span class="text-white text-xs">1</span>
                            </div>
                        </div>
                    </div>

                    <!-- Decorative Elements -->
                    <div class="absolute -z-10 -top-6 -left-6 w-24 h-24 bg-blue-100 dark:bg-blue-900/30 rounded-full opacity-50"></div>
                    <div class="absolute -z-10 -bottom-6 -right-6 w-32 h-32 bg-purple-100 dark:bg-purple-900/30 rounded-full opacity-50"></div>
                </div>

                <!-- Right side: Text Content -->
                <div class="space-y-8">
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 transform hover:-translate-y-1 transition-transform">
                        <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mb-6">
                            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Lightning Fast Generation</h3>
                        <p class="text-gray-600 dark:text-gray-300">Create professional QR codes with custom designs in seconds using our AI-powered platform.</p>
                    </div>

                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 transform hover:-translate-y-1 transition-transform">
                        <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mb-6">
                            <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z"/>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Advanced Customization</h3>
                        <p class="text-gray-600 dark:text-gray-300">Customize every aspect of your QR codes to match your brand identity perfectly.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div class="max-w-7xl mx-auto">
            <div class="grid md:grid-cols-2 gap-8">
                <!-- AI-Powered Sales Forecasting -->
                <div class="bg-white rounded-2xl shadow-sm p-8 hover:shadow-md transition-shadow">
                    <div class="grid grid-cols-6 gap-4 mb-8">
                        <!-- Integration Icons -->
                        <div class="p-2 rounded-full bg-gray-50 flex items-center justify-center">
                            <svg class="w-6 h-6 text-gray-600" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/></svg>
                        </div>
                        <div class="p-2 rounded-full bg-green-50 flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" viewBox="0 0 24 24" fill="currentColor"><path d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"/></svg>
                        </div>
                        <div class="p-2 rounded-full bg-blue-50 flex items-center justify-center">
                            <svg class="w-6 h-6 text-blue-600" viewBox="0 0 24 24" fill="currentColor"><path d="M13 10V3L4 14h7v7l9-11h-7z"/></svg>
                        </div>
                        <div class="p-2 rounded-full bg-purple-50 flex items-center justify-center">
                            <svg class="w-6 h-6 text-purple-600" viewBox="0 0 24 24" fill="currentColor"><path d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/></svg>
                        </div>
                        <div class="p-2 rounded-full bg-red-50 flex items-center justify-center">
                            <svg class="w-6 h-6 text-red-600" viewBox="0 0 24 24" fill="currentColor"><path d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/></svg>
                        </div>
                        <div class="p-2 rounded-full bg-yellow-50 flex items-center justify-center">
                            <svg class="w-6 h-6 text-yellow-600" viewBox="0 0 24 24" fill="currentColor"><path d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"/></svg>
                        </div>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">AI-Powered Sales Forecasting</h3>
                    <p class="text-gray-600">Track and analyze your team's performance in real-time with detailed analytics, key sales metrics, and actionable insights to drive smarter decision-making.</p>
                </div>

                <!-- Real-Time Performance Tracking -->
                <div class="bg-white rounded-2xl shadow-sm p-8 hover:shadow-md transition-shadow">
                    <div class="mb-8 h-48 flex items-end justify-between gap-2">
                        <div class="w-1/6 h-[30%] bg-gray-100 rounded-t"></div>
                        <div class="w-1/6 h-[45%] bg-gray-100 rounded-t"></div>
                        <div class="w-1/6 h-[60%] bg-gray-100 rounded-t"></div>
                        <div class="w-1/6 h-[85%] bg-orange-400 rounded-t"></div>
                        <div class="w-1/6 h-[40%] bg-gray-100 rounded-t"></div>
                        <div class="w-1/6 h-[25%] bg-gray-100 rounded-t"></div>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Real-Time Performance Tracking</h3>
                    <p class="text-gray-600">Sync effortlessly with Salesforce, HubSpot, and other major platforms to keep your data current and actionable.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-20 px-4 sm:px-6 lg:px-8 bg-white dark:bg-gray-900">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">What sales teams are saying</h2>
                <p class="text-xl text-gray-600 dark:text-gray-300">Trusted by high-performing sales teams worldwide</p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8">
                <!-- Loom Testimonial -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm p-8 hover:shadow-md transition-shadow">
                    <img src="https://placehold.co/80x30/blue/white?text=Loom" alt="Loom" class="h-8 mb-6 dark:invert">
                    <p class="text-gray-600 dark:text-gray-300 mb-6">"GemAI is by far my favorite automation tool going all the routine tasks. It's incredibly powerful yet remains surprisingly understandable. The efficiency and intelligence it brings to my workflow are unmatched!"</p>
                    <div class="flex items-center">
                        <img src="https://placehold.co/40x40" alt="Jake George" class="w-10 h-10 rounded-full mr-4">
                        <div>
                            <h4 class="font-semibold text-gray-900 dark:text-white">Jake George</h4>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">Founder, Loom</p>
                        </div>
                    </div>
                </div>

                <!-- Evernote Testimonial -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm p-8 hover:shadow-md transition-shadow">
                    <img src="https://placehold.co/80x30/blue/white?text=Evernote" alt="Evernote" class="h-8 mb-6 dark:invert">
                    <p class="text-gray-600 dark:text-gray-300 mb-6">"I recently started using GemAI to help maximize our sales pipeline, and the results have been impressive. It streamlines processes, boosts efficiency, and saves a ton of time."</p>
                    <div class="flex items-center">
                        <img src="https://placehold.co/40x40" alt="Steve Armenti" class="w-10 h-10 rounded-full mr-4">
                        <div>
                            <h4 class="font-semibold text-gray-900 dark:text-white">Steve Armenti</h4>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">VP Marketing, Evernote</p>
                        </div>
                    </div>
                </div>

                <!-- Lattice Testimonial -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm p-8 hover:shadow-md transition-shadow">
                    <img src="https://placehold.co/80x30/blue/white?text=Lattice" alt="Lattice" class="h-8 mb-6 dark:invert">
                    <p class="text-gray-600 dark:text-gray-300 mb-6">"I absolutely love building with GemAI—it has enabled us to scale our sales with powerful, production-ready workflows that drive efficiency for both my team and our clients. It's a game-changer for automation!"</p>
                    <div class="flex items-center">
                        <img src="https://placehold.co/40x40" alt="Sam Rahmanian" class="w-10 h-10 rounded-full mr-4">
                        <div>
                            <h4 class="font-semibold text-gray-900 dark:text-white">Sam Rahmanian</h4>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">Head of CRO, Lattice</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Blog Section -->
    <section class="py-20 px-4 sm:px-6 lg:px-8 bg-white dark:bg-gray-900">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-16">
                <span class="text-sm text-gray-400 dark:text-gray-500 block mb-3 border border-gray-200 dark:border-gray-700 inline-block px-5 py-2 rounded-lg">Blog</span>
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">Latest Insights & Updates</h2>
                <p class="text-xl text-gray-600 dark:text-gray-300">Stay informed with our latest articles and industry insights</p>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                <!-- Blog Post 1 -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                    <div class="relative">
                        <img src="https://placehold.co/600x400" alt="AI in Sales" class="w-full h-48 object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm">AI & ML</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-4">
                            <span>Mar 15, 2024</span>
                            <span class="mx-2">•</span>
                            <span>5 min read</span>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">How AI is Revolutionizing Sales Forecasting</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">Discover how artificial intelligence is transforming the way sales teams predict and achieve their targets with unprecedented accuracy.</p>
                        <div class="flex items-center">
                            <img src="https://placehold.co/40x40" alt="Sarah Johnson" class="w-10 h-10 rounded-full mr-4">
                            <div>
                                <h4 class="font-medium text-gray-900 dark:text-white">Sarah Johnson</h4>
                                <p class="text-sm text-gray-500 dark:text-gray-400">Sales AI Specialist</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Blog Post 2 -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                    <div class="relative">
                        <img src="https://placehold.co/600x400" alt="Sales Automation" class="w-full h-48 object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-purple-600 text-white px-3 py-1 rounded-full text-sm">Automation</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-4">
                            <span>Mar 12, 2024</span>
                            <span class="mx-2">•</span>
                            <span>4 min read</span>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">The Future of Sales Automation</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">Explore how automation tools are reshaping sales processes and enabling teams to focus on what matters most - building relationships.</p>
                        <div class="flex items-center">
                            <img src="https://placehold.co/40x40" alt="Michael Chen" class="w-10 h-10 rounded-full mr-4">
                            <div>
                                <h4 class="font-medium text-gray-900 dark:text-white">Michael Chen</h4>
                                <p class="text-sm text-gray-500 dark:text-gray-400">Product Manager</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Blog Post 3 -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                    <div class="relative">
                        <img src="https://placehold.co/600x400" alt="Data Analytics" class="w-full h-48 object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-green-600 text-white px-3 py-1 rounded-full text-sm">Analytics</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-4">
                            <span>Mar 10, 2024</span>
                            <span class="mx-2">•</span>
                            <span>6 min read</span>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">Data-Driven Sales Strategies</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">Learn how to leverage data analytics to make informed decisions and drive better sales outcomes in your organization.</p>
                        <div class="flex items-center">
                            <img src="https://placehold.co/40x40" alt="Emma Rodriguez" class="w-10 h-10 rounded-full mr-4">
                            <div>
                                <h4 class="font-medium text-gray-900 dark:text-white">Emma Rodriguez</h4>
                                <p class="text-sm text-gray-500 dark:text-gray-400">Data Analyst</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </section>

    <!-- Help Section -->
    <section class="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-800">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-16">
                <span class="text-sm text-gray-400 dark:text-gray-500 block mb-3 border border-gray-200 dark:border-gray-700 inline-block px-5 py-2 rounded-lg">Support</span>
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">How can we help you?</h2>
                <p class="text-xl text-gray-600 dark:text-gray-300">Get the support you need, when you need it</p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
                <!-- Documentation -->
                <div class="bg-white dark:bg-gray-900 rounded-2xl p-8 text-center hover:shadow-lg transition-shadow">
                    <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">Documentation</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-4">Comprehensive guides and API references</p>
                    <a href="#" class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300">View docs →</a>
                </div>

                <!-- Community -->
                <div class="bg-white dark:bg-gray-900 rounded-2xl p-8 text-center hover:shadow-lg transition-shadow">
                    <div class="w-16 h-16 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-8 h-8 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">Community</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-4">Join our community forum</p>
                    <a href="#" class="text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300">Join forum →</a>
                </div>

                <!-- Support -->
                <div class="bg-white dark:bg-gray-900 rounded-2xl p-8 text-center hover:shadow-lg transition-shadow">
                    <div class="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-8 h-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">Support</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-4">Get help from our support team</p>
                    <a href="#" class="text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300">Contact us →</a>
                </div>

                <!-- Status -->
                <div class="bg-white dark:bg-gray-900 rounded-2xl p-8 text-center hover:shadow-lg transition-shadow">
                    <div class="w-16 h-16 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-8 h-8 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">System Status</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-4">Check our system status</p>
                    <a href="#" class="text-yellow-600 dark:text-yellow-400 hover:text-yellow-700 dark:hover:text-yellow-300">View status →</a>
                </div>
            </div>

            <!-- Contact Options -->
            <div class="grid md:grid-cols-2 gap-8">
                <!-- Live Chat -->
                <div class="bg-white dark:bg-gray-900 rounded-2xl p-8">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Live Chat</h3>
                            <p class="text-gray-600 dark:text-gray-300">Available 24/7</p>
                        </div>
                    </div>
                    <button class="w-full bg-blue-600 dark:bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors">
                        Start Chat
                    </button>
                </div>

                <!-- Email Support -->
                <div class="bg-white dark:bg-gray-900 rounded-2xl p-8">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Email Support</h3>
                            <p class="text-gray-600 dark:text-gray-300">Response within 24 hours</p>
                        </div>
                    </div>
                    <button class="w-full bg-purple-600 dark:bg-purple-500 text-white px-6 py-3 rounded-lg hover:bg-purple-700 dark:hover:bg-purple-600 transition-colors">
                        Send Email
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-800">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">FAQs</h2>
                <p class="text-xl text-gray-600 dark:text-gray-300">Everything you need to know before getting started</p>
            </div>

            <div class="grid md:grid-cols-2 gap-12">
                <!-- Left side: Close Rate Visualization -->
                <div class="bg-white dark:bg-gray-900 rounded-2xl shadow-sm p-6">
                    <div class="border-b dark:border-gray-700 pb-4 mb-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Close Rate</h3>
                    </div>
                    <div class="space-y-4">
                        <div class="flex items-center gap-4">
                            <img src="https://placehold.co/40x40" alt="User Avatar" class="w-10 h-10 rounded-full">
                            <div class="flex-1 bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="font-medium text-gray-900 dark:text-white">Dev Porter</span>
                                    <span class="text-sm text-gray-500 dark:text-gray-400">3m ago</span>
                                </div>
                                <div class="h-2 bg-blue-600 dark:bg-blue-500 rounded-full w-3/4"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right side: FAQ Accordion -->
                <div class="space-y-4">
                    <!-- FAQ Item 1 (Expanded) -->
                    <div class="bg-white dark:bg-gray-900 rounded-xl shadow-sm">
                        <button class="w-full text-left px-6 py-4 flex items-center justify-between">
                            <span class="font-semibold text-gray-900 dark:text-white">How does your AI improve my sales process?</span>
                            <svg class="w-6 h-6 text-gray-500 dark:text-gray-400 transform rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </button>
                        <div class="px-6 pb-4">
                            <p class="text-gray-600 dark:text-gray-300">It analyzes customer interactions, forecasts trends, and provides actionable insights to close more deals. Our AI learns from your team's success patterns to optimize the entire sales pipeline.</p>
                        </div>
                    </div>

                    <!-- FAQ Item 2 -->
                    <div class="bg-white dark:bg-gray-900 rounded-xl shadow-sm">
                        <button class="w-full text-left px-6 py-4 flex items-center justify-between">
                            <span class="font-semibold text-gray-900 dark:text-white">Is my data secure?</span>
                            <svg class="w-6 h-6 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </button>
                    </div>

                    <!-- FAQ Item 3 -->
                    <div class="bg-white dark:bg-gray-900 rounded-xl shadow-sm">
                        <button class="w-full text-left px-6 py-4 flex items-center justify-between">
                            <span class="font-semibold text-gray-900 dark:text-white">Can I integrate this with my CRM?</span>
                            <svg class="w-6 h-6 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </button>
                    </div>

                    <!-- FAQ Item 4 -->
                    <div class="bg-white dark:bg-gray-900 rounded-xl shadow-sm">
                        <button class="w-full text-left px-6 py-4 flex items-center justify-between">
                            <span class="font-semibold text-gray-900 dark:text-white">Is there a free trial?</span>
                            <svg class="w-6 h-6 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-white to-pink-50 dark:from-gray-900 dark:to-gray-800">
        <div class="max-w-7xl mx-auto text-center">
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">Supercharge your sales with AI today!</h2>
            <button class="bg-blue-900 dark:bg-blue-700 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:bg-blue-800 dark:hover:bg-blue-600 transition-colors mb-16">
                Contact us
            </button>

            <!-- Dashboard Preview -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6 max-w-4xl mx-auto">
                <div class="flex items-center justify-between mb-8">
                    <div class="flex items-center space-x-4">
                        <span class="text-xl font-semibold text-gray-900 dark:text-white">Dashboard</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg">
                            <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
                            </svg>
                        </button>
                        <button class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg">
                            <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="grid grid-cols-3 gap-6 mb-8">
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-4">
                        <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">Sales</div>
                        <div class="text-2xl font-semibold text-gray-900 dark:text-white">$4,650.52</div>
                        <div class="text-sm text-green-600 dark:text-green-400 flex items-center mt-1">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                            </svg>
                            +12.5%
                        </div>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-4">
                        <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">Conversion Rate</div>
                        <div class="text-2xl font-semibold text-gray-900 dark:text-white">68%</div>
                        <div class="text-sm text-green-600 dark:text-green-400 flex items-center mt-1">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                            </svg>
                            +5.4%
                        </div>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-4">
                        <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">Average Deal</div>
                        <div class="text-2xl font-semibold text-gray-900 dark:text-white">$1,245.32</div>
                        <div class="text-sm text-green-600 dark:text-green-400 flex items-center mt-1">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                            </svg>
                            ****%
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Mobile Menu (Hidden by default) -->
    <div class="md:hidden fixed inset-0 bg-white dark:bg-gray-900 z-50 hidden">
        <div class="p-4">
            <div class="flex justify-between items-center mb-8">
                <a href="#" class="text-2xl font-bold text-blue-600 dark:text-blue-400">GemAI</a>
                <button class="text-gray-600 dark:text-gray-300">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <div class="space-y-4">
                <a href="#" class="block text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">Product</a>
                <a href="#" class="block text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">Pricing</a>
                <a href="#" class="block text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">Resources</a>
                <a href="#" class="block text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">Blog</a>
                <a href="#" class="block text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">Sign in</a>
                <a href="#" class="block bg-blue-600 dark:bg-blue-500 text-white px-4 py-2 rounded-lg text-center hover:bg-blue-700 dark:hover:bg-blue-600">Sign up</a>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white dark:bg-gray-900 text-gray-300">
        <!-- Main Footer -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-12">
                <!-- Company Info -->
                <div class="lg:col-span-2">
                    <a href="#" class="text-2xl font-bold text-black dark:text-white mb-6 block">GemAI</a>
                    <p class="text-gray-400 dark:text-gray-500 mb-8 max-w-md">Empowering sales teams with AI-driven insights and automation to close more deals and drive unprecedented growth.</p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path fill-rule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.015 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clip-rule="evenodd"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path fill-rule="evenodd" d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" clip-rule="evenodd"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Product Links -->
                <div>
                    <h3 class="text-black dark:text-white font-semibold mb-4">Product</h3>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Features</a></li>
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Pricing</a></li>
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Security</a></li>
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Enterprise</a></li>
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Customer Stories</a></li>
                    </ul>
                </div>

                <!-- Resources Links -->
                <div>
                    <h3 class="text-black dark:text-white font-semibold mb-4">Resources</h3>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Documentation</a></li>
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">API Reference</a></li>
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Blog</a></li>
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Community</a></li>
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Support</a></li>
                    </ul>
                </div>

                <!-- Company Links -->
                <div>
                    <h3 class="text-black dark:text-white font-semibold mb-4">Company</h3>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">About Us</a></li>
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Careers</a></li>
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Partners</a></li>
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Contact</a></li>
                        <li><a href="#" class="text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Press Kit</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Bottom Footer -->
        <div class="border-t border-gray-100 dark:border-gray-800">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="md:flex md:items-center md:justify-between">
                    <div class="text-sm text-gray-400 dark:text-gray-500">
                        © 2024 GemAI. All rights reserved.
                    </div>
                    <div class="flex space-x-6 mt-4 md:mt-0">
                        <a href="#" class="text-sm text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Privacy Policy</a>
                        <a href="#" class="text-sm text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Terms of Service</a>
                        <a href="#" class="text-sm text-gray-400 dark:text-gray-500 hover:text-black dark:hover:text-white transition-colors">Cookie Policy</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        const menuButton = document.getElementById('menubtn');
        const mobileMenu = document.getElementById('mobilebtn');
        
        menuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });

        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        
        
        darkModeToggle.addEventListener('click', () => {
            if (document.querySelector('html').getAttribute('data-theme') == 'dark') {
                document.querySelector('html').setAttribute('data-theme', 'light')
                localStorage.theme = 'light';
            } else {
                document.querySelector('html').setAttribute('data-theme', 'dark')
                localStorage.theme = 'dark';
            }
        });
    </script>
</body>
</html>
