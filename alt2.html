<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinkPulse - Modern URL Shortener</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
        }
        .glass {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .gradient-text {
            background: linear-gradient(90deg, #f472b6 0%, #818cf8 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .gradient-border {
            border: 1px solid transparent;
            background: linear-gradient(#0f172a, #0f172a) padding-box,
                        linear-gradient(90deg, #f472b6, #818cf8) border-box;
        }
        .gradient-btn {
            background: linear-gradient(90deg, #f472b6 0%, #818cf8 100%);
        }
        .floating {
            animation: float 6s ease-in-out infinite;
        }
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-15px); }
            100% { transform: translateY(0px); }
        }
    </style>
</head>
<body class="text-gray-200 min-h-screen">
    <!-- Navigation -->
    <nav class="glass fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <div class="flex items-center">
                    <a href="#" class="text-2xl font-bold gradient-text">LinkPulse</a>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#features" class="hover:text-pink-400 transition-colors">Features</a>
                    <a href="#pricing" class="hover:text-pink-400 transition-colors">Pricing</a>
                    <a href="#" class="hover:text-pink-400 transition-colors">Resources</a>
                    <a href="#" class="hover:text-pink-400 transition-colors">Enterprise</a>
                </div>
                <div class="hidden md:flex items-center space-x-4">
                    <a href="#" class="hover:text-pink-400 transition-colors">Login</a>
                    <a href="#" class="gradient-btn px-5 py-2 rounded-full font-medium hover:opacity-90 transition-opacity">Sign Up Free</a>
                </div>
                <div class="md:hidden">
                    <button id="menubtn" class="text-gray-200">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="pt-40 pb-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
        <div class="absolute inset-0 z-0 opacity-30">
            <div class="absolute inset-0 bg-[radial-gradient(#818cf8_1px,transparent_1px)] [background-size:20px_20px]"></div>
        </div>
        
        <div class="max-w-7xl mx-auto relative z-10">
            <div class="text-center mb-12">
                <h1 class="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    Short links with <span class="gradient-text">powerful insights</span>
                </h1>
                <p class="text-xl text-gray-400 mb-10 max-w-3xl mx-auto">
                    Transform your long URLs into memorable, trackable links that help you understand your audience and optimize your marketing.
                </p>
            </div>
            
            <!-- URL Shortener Input -->
            <div class="glass rounded-2xl p-6 max-w-4xl mx-auto mb-16 shadow-lg">
                <div class="flex flex-col md:flex-row gap-4">
                    <input type="text" placeholder="Paste your long URL here" class="flex-1 bg-gray-800/50 border border-gray-700 rounded-xl px-4 py-3 focus:outline-none focus:ring-2 focus:ring-pink-500/50">
                    <button class="gradient-btn px-8 py-3 rounded-xl font-semibold hover:opacity-90 transition-opacity whitespace-nowrap">
                        Shorten URL
                    </button>
                </div>
                
                <!-- Preview of shortened URL -->
                <div class="mt-6 bg-gray-800/30 rounded-xl p-4 hidden md:block">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 rounded-full bg-pink-500"></div>
                            <span class="text-gray-400">Original:</span>
                            <span class="text-gray-300 truncate max-w-xs">https://example.com/very/long/url/that/needs/shortening?param=value</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between mt-3">
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 rounded-full bg-indigo-500"></div>
                            <span class="text-gray-400">Shortened:</span>
                            <span class="text-white font-medium">linkpulse.io/b4k9z</span>
                        </div>
                        <button class="text-indigo-400 hover:text-indigo-300 transition-colors">
                            Copy
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Floating Analytics Cards -->
            <div class="relative h-64 md:h-80 mb-20">
                <div class="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full">
                    <div class="glass rounded-xl p-4 shadow-lg max-w-xs mx-auto floating" style="animation-delay: 0s;">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="font-semibold">Click Analytics</h3>
                            <span class="text-xs text-indigo-400">Live</span>
                        </div>
                        <div class="h-24 bg-gradient-to-r from-indigo-500/20 to-pink-500/20 rounded-lg mb-2"></div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-400">Total Clicks:</span>
                            <span class="font-medium">1,248</span>
                        </div>
                    </div>
                    
                    <div class="glass rounded-xl p-4 shadow-lg max-w-xs mx-auto absolute -top-10 -right-20 md:right-10 floating" style="animation-delay: 1s;">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="font-semibold">Location Data</h3>
                            <span class="text-xs text-pink-400">Updated</span>
                        </div>
                        <div class="flex items-center justify-between text-sm mb-2">
                            <span class="text-gray-400">United States:</span>
                            <span class="font-medium">42%</span>
                        </div>
                        <div class="flex items-center justify-between text-sm mb-2">
                            <span class="text-gray-400">Europe:</span>
                            <span class="font-medium">31%</span>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-400">Asia:</span>
                            <span class="font-medium">18%</span>
                        </div>
                    </div>
                    
                    <div class="glass rounded-xl p-4 shadow-lg max-w-xs mx-auto absolute -bottom-10 -left-20 md:left-10 floating" style="animation-delay: 2s;">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="font-semibold">Device Breakdown</h3>
                            <span class="text-xs text-indigo-400">Live</span>
                        </div>
                        <div class="flex items-center justify-between text-sm mb-2">
                            <span class="text-gray-400">Mobile:</span>
                            <span class="font-medium">64%</span>
                        </div>
                        <div class="flex items-center justify-between text-sm mb-2">
                            <span class="text-gray-400">Desktop:</span>
                            <span class="font-medium">29%</span>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-400">Tablet:</span>
                            <span class="font-medium">7%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 px-4 sm:px-6 lg:px-8 bg-gray-800/50">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">Powerful features for link management</h2>
                <p class="text-xl text-white max-w-3xl mx-auto">Everything you need to create, track, and optimize your links</p>
            </div>
            
            <!-- URL Shortener Feature -->
            <div class="grid md:grid-cols-2 gap-12 items-center my-40 fade-in" data-aos="fade-up">
                <div class="order-2 md:order-1">
                    <div class="inline-block px-4 py-1 bg-pink-100 text-pink-600 rounded-full text-sm font-medium mb-4">URL Shortener</div>
                    <h3 class="text-2xl md:text-3xl font-bold text-white mb-4">Create branded short links with powerful analytics</h3>
                    <p class="text-lg text-white mb-6">Transform long, unwieldy links into clean, memorable URLs that reflect your brand. Track clicks, geographic data, and referral sources in real-time.</p>
                    <ul class="space-y-3 mb-8">
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="text-white">Custom domains and branded links</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="text-white">Detailed click analytics and insights</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="text-white">UTM parameter builder and tracking</span>
                        </li>
                    </ul>
                    <a href="#" class="inline-flex items-center text-purple-600 font-medium hover:text-purple-700">
                        Learn more
                        <svg class="w-5 h-5 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                        </svg>
                    </a>
                </div>
                <div class="order-1 md:order-2 float">
                    <div class="glass rounded-2xl p-6 shadow-lg">
                        <div class="bg-transparent rounded-xl shadow-sm p-6">
                            <div class="flex items-center space-x-2 mb-4">
                                <div class="w-3 h-3 rounded-full bg-red-500"></div>
                                <div class="w-3 h-3 rounded-full bg-yellow-500"></div>
                                <div class="w-3 h-3 rounded-full bg-green-500"></div>
                                <div class="ml-auto text-sm text-gray-400">URL Shortener</div>
                            </div>
                            <div class="glass bg-transparent rounded-lg p-4 mb-4">
                                <div class="flex items-center mb-3">
                                    <span class="text-gray-400 mr-2">https://</span>
                                    <span class="bg-transparent flex-1 text-white">example.com/very/long/url/that/needs/shortening</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <span class="text-sm text-gray-500">syncly.io/</span>
                                        <span class="bg-transparent text-sm text-white">product-demo</span>
                                    </div>
                                    <button class="bg-purple-600 text-white px-4 py-2 rounded-lg text-sm">
                                        Shorten
                                    </button>
                                </div>
                            </div>
                            <div class="glass flex items-center justify-between p-3 rounded-lg">
                                <div>
                                    <div class="text-purple-200 font-medium">syncly.io/product-demo</div>
                                    <div class="text-xs text-gray-200">Created 2 minutes ago • 24 clicks</div>
                                </div>
                                <button class="text-gray-400 hover:text-white">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- QR Codes Feature -->
            <div class="grid md:grid-cols-2 gap-12 items-center my-24" data-aos="fade-up">
                <div class="float">
                    <div class="glass rounded-2xl p-6 shadow-lg">
                        <div class="rounded-xl shadow-sm p-6">
                            <div class="flex items-center space-x-2 mb-4">
                                <div class="w-3 h-3 rounded-full bg-red-500"></div>
                                <div class="w-3 h-3 rounded-full bg-yellow-500"></div>
                                <div class="w-3 h-3 rounded-full bg-green-500"></div>
                                <div class="ml-auto text-sm text-gray-400">QR Code Generator</div>
                            </div>
                            <div class="glass flex justify-center mb-4">
                                <div class="glass w-48 h-48  border rounded-lg p-4 flex items-center justify-center">
                                    <svg class="w-full h-full" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="10" y="10" width="30" height="30" rx="2" fill="#4F46E5" />
                                        <rect x="60" y="10" width="30" height="30" rx="2" fill="#4F46E5" />
                                        <rect x="10" y="60" width="30" height="30" rx="2" fill="#4F46E5" />
                                        <rect x="20" y="20" width="10" height="10" rx="1" fill="white" />
                                        <rect x="70" y="20" width="10" height="10" rx="1" fill="white" />
                                        <rect x="20" y="70" width="10" height="10" rx="1" fill="white" />
                                        <rect x="60" y="60" width="10" height="10" rx="1" fill="#4F46E5" />
                                        <rect x="80" y="60" width="10" height="10" rx="1" fill="#4F46E5" />
                                        <rect x="60" y="80" width="30" height="10" rx="1" fill="#4F46E5" />
                                    </svg>
                                </div>
                            </div>
                            <div class="flex justify-between">
                                <button class="bg-gray-100 text-white px-3 py-2 rounded-lg text-sm flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                                    </svg>
                                    Download
                                </button>
                                <button class="bg-indigo-600 text-white px-3 py-2 rounded-lg text-sm">
                                    Customize
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <div class="inline-block px-4 py-1 bg-indigo-100 text-indigo-600 rounded-full text-sm font-medium mb-4">QR Codes</div>
                    <h3 class="text-2xl md:text-3xl font-bold text-white mb-4">Generate dynamic QR codes with tracking</h3>
                    <p class="text-lg text-white mb-6">Create customizable QR codes that connect your offline materials to your online presence. Update destinations without reprinting and track scans in real-time.</p>
                    <ul class="space-y-3 mb-8">
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="text-white">Customizable designs and branding</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="text-white">Dynamic QR codes with editable destinations</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="text-white">Scan analytics and location tracking</span>
                        </li>
                    </ul>
                    <a href="#" class="inline-flex items-center text-indigo-600 font-medium hover:text-indigo-700">
                        Learn more
                        <svg class="w-5 h-5 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                        </svg>
                    </a>
                </div>
            </div>
            
            <!-- Link-in Bio Feature -->
            <div class="grid md:grid-cols-2 gap-12 items-center" data-aos="fade-up">
                <div class="order-2 md:order-1">
                    <div class="inline-block px-4 py-1 bg-green-100 text-green-600 rounded-full text-sm font-medium mb-4">Link-in Bio</div>
                    <h3 class="text-2xl md:text-3xl font-bold text-white mb-4">Create beautiful bio pages in minutes</h3>
                    <p class="text-lg text-white mb-6">Build a customizable landing page that houses all your important links in one place. Perfect for social media profiles, marketing campaigns, and personal branding.</p>
                    <ul class="space-y-3 mb-8">
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="text-white">Customizable themes and layouts</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="text-white">Social media integration</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="text-white">Visitor analytics and insights</span>
                        </li>
                    </ul>
                    <a href="#" class="inline-flex items-center text-green-600 font-medium hover:text-green-700">
                        Learn more
                        <svg class="w-5 h-5 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                        </svg>
                    </a>
                </div>
                <div class="order-1 md:order-2 float">
                    <div class="bg-gradient-to-br from-green-50 to-teal-50 rounded-2xl p-6 shadow-lg">
                        <div class="bg-white rounded-xl shadow-sm p-6">
                            <div class="flex items-center space-x-2 mb-4">
                                <div class="w-3 h-3 rounded-full bg-red-500"></div>
                                <div class="w-3 h-3 rounded-full bg-yellow-500"></div>
                                <div class="w-3 h-3 rounded-full bg-green-500"></div>
                                <div class="ml-auto text-sm text-gray-400">Bio Page</div>
                            </div>
                            <div class="flex justify-center mb-4">
                                <div class="w-48 bg-gray-50 rounded-xl overflow-hidden">
                                    <div class="h-16 bg-gradient-to-r from-green-400 to-blue-500"></div>
                                    <div class="p-4 flex flex-col items-center">
                                        <div class="w-16 h-16 bg-white rounded-full border-4 border-white -mt-10 mb-2 overflow-hidden">
                                            <div class="w-full h-full bg-gray-200"></div>
                                        </div>
                                        <h3 class="font-bold text-gray-800">@username</h3>
                                        <p class="text-xs text-gray-500 text-center mt-1 mb-3">Digital creator & content specialist</p>
                                        <div class="space-y-2 w-full">
                                            <div class="bg-white border rounded-lg p-2 text-xs text-center">Latest Video</div>
                                            <div class="bg-white border rounded-lg p-2 text-xs text-center">Shop My Products</div>
                                            <div class="bg-white border rounded-lg p-2 text-xs text-center">Contact Me</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="py-20 px-4 sm:px-6 lg:px-8 bg-gray-800/50">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">Flexible pricing plans</h2>
                <p class="text-xl text-gray-400 max-w-3xl mx-auto">Choose the plan that best fits your needs</p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Pricing Card 1 -->
                <div class="glass rounded-2xl overflow-hidden hover:shadow-lg transition-shadow">
                    <div class="bg-gradient-to-br from-pink-500/20 to-indigo-500/20 p-6">
                        <div class="w-12 h-12 rounded-full bg-pink-500/20 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-pink-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 015.656 0l4 4a4 4 0 01-5.656 5.656l-1.102-1.101" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2">Starter</h3>
                        <p class="text-gray-400 mb-4">Perfect for individuals and small teams</p>
                        <div class="flex items-center justify-between mb-6">
                            <span class="text-3xl font-bold">$0</span>
                            <span class="text-gray-400">/ month</span>
                        </div>
                        <ul class="mb-6">
                            <li class="flex items-center mb-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-400">Unlimited URL shortening</span>
                            </li>
                            <li class="flex items-center mb-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-400">Basic analytics</span>
                            </li>
                            <li class="flex items-center mb-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-400">Custom domains</span>
                            </li>
                        </ul>
                        <a href="#" class="gradient-btn px-8 py-3 rounded-xl font-semibold hover:opacity-90 transition-opacity block w-full text-center">Get Started</a>
                    </div>
                </div>
                
                <!-- Pricing Card 2 -->
                <div class="glass rounded-2xl overflow-hidden hover:shadow-lg transition-shadow">
                    <div class="bg-gradient-to-br from-indigo-500/20 to-purple-500/20 p-6">
                        <div class="w-12 h-12 rounded-full bg-indigo-500/20 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2">Pro</h3>
                        <p class="text-gray-400 mb-4">For businesses and teams</p>
                        <div class="flex items-center justify-between mb-6">
                            <span class="text-3xl font-bold">$19</span>
                            <span class="text-gray-400">/ month</span>
                        </div>
                        <ul class="mb-6">
                            <li class="flex items-center mb-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-400">Unlimited URL shortening</span>
                            </li>
                            <li class="flex items-center mb-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-400">Advanced analytics</span>
                            </li>
                            <li class="flex items-center mb-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-400">Custom domains</span>
                            </li>
                        </ul>
                        <a href="#" class="gradient-btn px-8 py-3 rounded-xl font-semibold hover:opacity-90 transition-opacity block w-full text-center">Get Started</a>
                    </div>
                </div>
                
                <!-- Pricing Card 3 -->
                <div class="glass rounded-2xl overflow-hidden hover:shadow-lg transition-shadow">
                    <div class="bg-gradient-to-br from-purple-500/20 to-pink-500/20 p-6">
                        <div class="w-12 h-12 rounded-full bg-purple-500/20 flex items-center justify-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2">Enterprise</h3>
                        <p class="text-gray-400 mb-4">For large organizations</p>
                        <div class="flex items-center justify-between mb-6">
                            <span class="text-3xl font-bold">$49</span>
                            <span class="text-gray-400">/ month</span>
                        </div>
                        <ul class="mb-6">
                            <li class="flex items-center mb-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-400">Unlimited URL shortening</span>
                            </li>
                            <li class="flex items-center mb-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-400">Advanced analytics</span>
                            </li>
                            <li class="flex items-center mb-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-400">Custom domains</span>
                            </li>
                        </ul>
                        <a href="#" class="gradient-btn px-8 py-3 rounded-xl font-semibold hover:opacity-90 transition-opacity block w-full text-center">Get Started</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Integrations Section -->
    <section id="integrations" class="py-20 px-4 sm:px-6 lg:px-8 bg-gray-800/50">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Seamless integrations</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">Connect with your favorite tools and platforms</p>
            </div>
            
            <!-- Integration Icons in Transit-like Structure -->
            <div class="relative max-w-4xl mx-auto">
                <!-- Horizontal Line -->
                <div class="absolute top-1/2 left-0 right-0 h-0.5 bg-gray-200 transform -translate-y-1/2"></div>
                
                <!-- Integration Icons -->
                <div class="flex justify-between relative">
                    <!-- Twitter/X -->
                    <div class="bg-white rounded-full p-4 shadow-md relative z-10 pulse" style="animation-delay: 0.1s;">
                        <svg class="w-8 h-8 text-blue-400" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"></path>
                        </svg>
                    </div>
                    
                    <!-- Instagram -->
                    <div class="bg-white rounded-full p-4 shadow-md relative z-10 pulse" style="animation-delay: 0.2s;">
                        <svg class="w-8 h-8 text-pink-500" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                    </div>
                    
                    <!-- Facebook -->
                    <div class="bg-white rounded-full p-4 shadow-md relative z-10 pulse" style="animation-delay: 0.3s;">
                        <svg class="w-8 h-8 text-blue-600" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                        </svg>
                    </div>
                    
                    <!-- LinkedIn -->
                    <div class="bg-white rounded-full p-4 shadow-md relative z-10 pulse" style="animation-delay: 0.4s;">
                        <svg class="w-8 h-8 text-blue-700" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                        </svg>
                    </div>
                    
                    <!-- TikTok -->
                    <div class="bg-white rounded-full p-4 shadow-md relative z-10 pulse" style="animation-delay: 0.5s;">
                        <svg class="w-8 h-8 text-black" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
                        </svg>
                    </div>
                    
                    <!-- YouTube -->
                    <div class="bg-white rounded-full p-4 shadow-md relative z-10 pulse" style="animation-delay: 0.6s;">
                        <svg class="w-8 h-8 text-red-600" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="glass py-16 px-4 sm:px-6 lg:px-8 relative">
        <div class="absolute inset-0 z-0 opacity-30">
            <div class="absolute inset-0 bg-[radial-gradient(#818cf8_1px,transparent_1px)] [background-size:20px_20px]"></div>
        </div>
        
        <div class="max-w-7xl mx-auto relative z-10">
            <!-- Glass Footer Container -->
            <div class="glass rounded-3xl p-8 shadow-lg">
                <div class="grid md:grid-cols-4 gap-8">
                    <!-- Company Info -->
                    <div class="md:col-span-2">
                        <a href="#" class="text-2xl font-bold gradient-text mb-6 inline-block">LinkPulse</a>
                        <p class="text-gray-400 mb-6 max-w-md">
                            Transform your long URLs into memorable, trackable links that help you understand your audience and optimize your marketing.
                        </p>
                        <div class="flex space-x-4">
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"></path>
                                </svg>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                                </svg>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                </svg>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                    
                    <!-- Product Links -->
                    <div>
                        <h3 class="text-white font-semibold mb-4">Product</h3>
                        <ul class="space-y-3">
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">URL Shortener</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">QR Codes</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Link-in Bio</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Analytics</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">API</a></li>
                        </ul>
                    </div>
                    
                    <!-- Company Links -->
                    <div>
                        <h3 class="text-white font-semibold mb-4">Company</h3>
                        <ul class="space-y-3">
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">About Us</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Careers</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Blog</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Press</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Contact</a></li>
                        </ul>
                    </div>
                </div>
                
                <div class="border-t border-gray-700 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
                    <div class="text-gray-400 mb-4 md:mb-0">
                        © 2024 LinkPulse. All rights reserved.
                    </div>
                    <div class="flex space-x-6">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">Terms</a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">Privacy</a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">Cookies</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Mobile Menu (Hidden by default) -->
    <div id="mobilebtn" class="fixed inset-0 bg-gray-900 z-50 hidden">
        <div class="p-4">
            <div class="flex justify-between items-center mb-8">
                <a href="#" class="text-2xl font-bold gradient-text">LinkPulse</a>
                <button class="text-gray-200">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <div class="space-y-4">
                <a href="#features" class="block text-gray-200 hover:text-pink-400 transition-colors">Features</a>
                <a href="#pricing" class="block text-gray-200 hover:text-pink-400 transition-colors">Pricing</a>
                <a href="#" class="block text-gray-200 hover:text-pink-400 transition-colors">Resources</a>
                <a href="#" class="block text-gray-200 hover:text-pink-400 transition-colors">Enterprise</a>
                <div class="pt-4 border-t border-gray-700">
                    <a href="#" class="block text-gray-200 hover:text-pink-400 transition-colors mb-4">Login</a>
                    <a href="#" class="gradient-btn block text-center px-5 py-2 rounded-full font-medium hover:opacity-90 transition-opacity">Sign Up Free</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Mobile menu toggle
        const menuButton = document.getElementById('menubtn');
        const mobileMenu = document.getElementById('mobilebtn');
        
        menuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });
        
        // Close mobile menu when clicking on a link
        const mobileLinks = document.querySelectorAll('#mobilebtn a');
        mobileLinks.forEach(link => {
            link.addEventListener('click', () => {
                mobileMenu.classList.add('hidden');
            });
        });
        
        // Close button in mobile menu
        const closeButton = document.querySelector('#mobilebtn button');
        closeButton.addEventListener('click', () => {
            mobileMenu.classList.add('hidden');
        });
    </script>
</body>
</html>